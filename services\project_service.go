package services

import (
	"cloud-data-manager/database"
	"cloud-data-manager/models"
	"database/sql"
	"fmt"
)

// ProjectService 项目服务
type ProjectService struct{}

// NewProjectService 创建项目服务实例
func NewProjectService() *ProjectService {
	return &ProjectService{}
}

// GetAllProjects 获取所有项目
func (s *ProjectService) GetAllProjects() ([]models.Project, error) {
	query := `SELECT id, name, description, aes_key, created_at, updated_at FROM projects ORDER BY created_at DESC`
	rows, err := database.DB.Query(query)
	if err != nil {
		return nil, fmt.Errorf("failed to query projects: %v", err)
	}
	defer rows.Close()

	var projects []models.Project
	for rows.Next() {
		var project models.Project
		err := rows.Scan(&project.ID, &project.Name, &project.Description, &project.AESKey, &project.CreatedAt, &project.UpdatedAt)
		if err != nil {
			return nil, fmt.Errorf("failed to scan project: %v", err)
		}
		projects = append(projects, project)
	}

	return projects, nil
}

// GetProjectByID 根据ID获取项目
func (s *ProjectService) GetProjectByID(id string) (*models.Project, error) {
	query := `SELECT id, name, description, aes_key, created_at, updated_at FROM projects WHERE id = ?`
	row := database.DB.QueryRow(query, id)

	var project models.Project
	err := row.Scan(&project.ID, &project.Name, &project.Description, &project.AESKey, &project.CreatedAt, &project.UpdatedAt)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("project not found")
		}
		return nil, fmt.Errorf("failed to get project: %v", err)
	}

	return &project, nil
}

// CreateProject 创建项目
func (s *ProjectService) CreateProject(req models.CreateProjectRequest) error {
	// 如果没有提供AES密钥，使用默认值
	aesKey := req.AESKey
	if aesKey == "" {
		aesKey = "zmxy2024cloudkey"
	}

	query := `INSERT INTO projects (id, name, description, aes_key) VALUES (?, ?, ?, ?)`
	_, err := database.DB.Exec(query, req.ID, req.Name, req.Description, aesKey)
	if err != nil {
		return fmt.Errorf("failed to create project: %v", err)
	}

	// 不再创建默认数据表，让用户手动创建

	return nil
}

// UpdateProject 更新项目
func (s *ProjectService) UpdateProject(id string, req models.UpdateProjectRequest) error {
	query := `UPDATE projects SET name = ?, description = ?, aes_key = ? WHERE id = ?`
	result, err := database.DB.Exec(query, req.Name, req.Description, req.AESKey, id)
	if err != nil {
		return fmt.Errorf("failed to update project: %v", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %v", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("project not found")
	}

	return nil
}

// DeleteProject 删除项目
func (s *ProjectService) DeleteProject(id string) error {
	// 开始事务
	tx, err := database.DB.Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %v", err)
	}
	defer tx.Rollback()

	// 首先检查项目是否存在
	var count int
	err = tx.QueryRow("SELECT COUNT(*) FROM projects WHERE id = ?", id).Scan(&count)
	if err != nil {
		return fmt.Errorf("failed to check project existence: %v", err)
	}
	if count == 0 {
		return fmt.Errorf("project not found")
	}

	// 删除相关的数据项
	_, err = tx.Exec("DELETE FROM data_items WHERE project_id = ?", id)
	if err != nil {
		return fmt.Errorf("failed to delete data items: %v", err)
	}

	// 删除相关的数据表
	_, err = tx.Exec("DELETE FROM data_tables WHERE project_id = ?", id)
	if err != nil {
		return fmt.Errorf("failed to delete data tables: %v", err)
	}

	// 删除项目
	_, err = tx.Exec("DELETE FROM projects WHERE id = ?", id)
	if err != nil {
		return fmt.Errorf("failed to delete project: %v", err)
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		return fmt.Errorf("failed to commit transaction: %v", err)
	}

	return nil
}
