package controllers

import (
	"cloud-data-manager/services"
	"net/http"

	"github.com/gin-gonic/gin"
)

// LuaController Lua数据控制器
type LuaController struct {
	luaService *services.LuaService
}

// NewLuaController 创建Lua数据控制器实例
func NewLuaController() *LuaController {
	return &LuaController{
		luaService: services.NewLuaService(),
	}
}

// GetLuaData 获取Lua格式数据（需要权限验证）
func (c *LuaController) GetLuaData(ctx *gin.Context) {
	projectID := ctx.Param("projectId")

	// 验证项目是否存在
	projectService := services.NewProjectService()
	_, err := projectService.GetProjectByID(projectID)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "Project not found"})
		return
	}

	luaData, err := c.luaService.GenerateLuaData(projectID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 返回纯文本格式的Lua数据
	ctx.Header("Content-Type", "text/plain; charset=utf-8")
	ctx.String(http.StatusOK, luaData)
}

// GetLuaDataPreview 获取Lua格式数据预览（无需权限验证）
func (c *LuaController) GetLuaDataPreview(ctx *gin.Context) {
	projectID := ctx.Param("projectId")

	// 验证项目是否存在
	projectService := services.NewProjectService()
	_, err := projectService.GetProjectByID(projectID)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "Project not found"})
		return
	}

	luaData, err := c.luaService.GenerateLuaData(projectID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 返回JSON格式，包含data字段
	ctx.JSON(http.StatusOK, gin.H{"data": luaData})
}
