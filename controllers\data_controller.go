package controllers

import (
	"cloud-data-manager/models"
	"cloud-data-manager/services"
	"net/http"

	"github.com/gin-gonic/gin"
)

// DataController 数据控制器
type DataController struct {
	dataTableService *services.DataTableService
	dataItemService  *services.DataItemService
}

// NewDataController 创建数据控制器实例
func NewDataController() *DataController {
	return &DataController{
		dataTableService: services.NewDataTableService(),
		dataItemService:  services.NewDataItemService(),
	}
}

// GetDataTables 获取项目的所有数据表
func (c *DataController) GetDataTables(ctx *gin.Context) {
	projectID := ctx.Param("projectId")
	
	tables, err := c.dataTableService.GetDataTablesByProject(projectID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"data": tables})
}

// CreateDataTable 创建数据表
func (c *DataController) CreateDataTable(ctx *gin.Context) {
	projectID := ctx.Param("projectId")
	
	var req models.CreateDataTableRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	err := c.dataTableService.CreateDataTable(projectID, req)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusCreated, gin.H{"message": "Data table created successfully"})
}

// UpdateDataTable 更新数据表
func (c *DataController) UpdateDataTable(ctx *gin.Context) {
	projectID := ctx.Param("projectId")
	tableName := ctx.Param("tableName")
	
	var req models.CreateDataTableRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	err := c.dataTableService.UpdateDataTable(projectID, tableName, req)
	if err != nil {
		if err.Error() == "data table not found" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": "Data table not found"})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"message": "Data table updated successfully"})
}

// DeleteDataTable 删除数据表
func (c *DataController) DeleteDataTable(ctx *gin.Context) {
	projectID := ctx.Param("projectId")
	tableName := ctx.Param("tableName")

	err := c.dataTableService.DeleteDataTable(projectID, tableName)
	if err != nil {
		if err.Error() == "data table not found" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": "Data table not found"})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"message": "Data table deleted successfully"})
}

// GetDataItems 获取数据表的所有数据项
func (c *DataController) GetDataItems(ctx *gin.Context) {
	projectID := ctx.Param("projectId")
	tableName := ctx.Param("tableName")
	
	items, err := c.dataItemService.GetDataItems(projectID, tableName)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"data": items})
}

// GetDataItem 获取指定数据项
func (c *DataController) GetDataItem(ctx *gin.Context) {
	projectID := ctx.Param("projectId")
	tableName := ctx.Param("tableName")
	itemKey := ctx.Param("itemKey")
	
	item, err := c.dataItemService.GetDataItem(projectID, tableName, itemKey)
	if err != nil {
		if err.Error() == "data item not found" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": "Data item not found"})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"data": item})
}

// CreateDataItem 创建数据项
func (c *DataController) CreateDataItem(ctx *gin.Context) {
	projectID := ctx.Param("projectId")
	tableName := ctx.Param("tableName")
	
	var req models.CreateDataItemRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	err := c.dataItemService.CreateDataItem(projectID, tableName, req)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusCreated, gin.H{"message": "Data item created successfully"})
}

// UpdateDataItem 更新数据项
func (c *DataController) UpdateDataItem(ctx *gin.Context) {
	projectID := ctx.Param("projectId")
	tableName := ctx.Param("tableName")
	itemKey := ctx.Param("itemKey")
	
	var req models.UpdateDataItemRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	err := c.dataItemService.UpdateDataItem(projectID, tableName, itemKey, req)
	if err != nil {
		if err.Error() == "data item not found" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": "Data item not found"})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"message": "Data item updated successfully"})
}

// DeleteDataItem 删除数据项
func (c *DataController) DeleteDataItem(ctx *gin.Context) {
	projectID := ctx.Param("projectId")
	tableName := ctx.Param("tableName")
	itemKey := ctx.Param("itemKey")

	err := c.dataItemService.DeleteDataItem(projectID, tableName, itemKey)
	if err != nil {
		if err.Error() == "data item not found" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": "Data item not found"})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"message": "Data item deleted successfully"})
}
