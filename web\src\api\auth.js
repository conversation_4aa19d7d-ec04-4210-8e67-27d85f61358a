import axios from 'axios'

// 创建axios实例
const api = axios.create({
  baseURL: 'http://localhost:18082/api',
  timeout: 10000,
  withCredentials: true
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 从localStorage获取token
    const user = localStorage.getItem('user')
    if (user) {
      try {
        const userData = JSON.parse(user)
        if (userData.token) {
          config.headers.Authorization = `Bearer ${userData.token}`
        }
      } catch (error) {
        console.error('解析用户数据失败:', error)
        localStorage.removeItem('user')
      }
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    if (error.response?.status === 401) {
      // 未授权，清除本地存储的用户信息
      localStorage.removeItem('user')
      
      // 如果不是登录页面，跳转到登录页面
      if (window.location.pathname !== '/login') {
        window.location.reload()
      }
    }
    return Promise.reject(error)
  }
)

// 认证相关API
export const authAPI = {
  // 登录
  login: (credentials) => {
    return api.post('/auth/login', credentials)
  },
  
  // 登出
  logout: () => {
    return api.post('/auth/logout')
  },
  
  // 获取用户信息
  getUserInfo: () => {
    return api.get('/auth/user')
  },
  
  // 刷新token
  refreshToken: () => {
    return api.post('/auth/refresh')
  },
  
  // 检查认证状态
  checkAuth: () => {
    return api.get('/auth/check')
  }
}

// 工具函数
export const authUtils = {
  // 检查是否已登录
  isLoggedIn: () => {
    const user = localStorage.getItem('user')
    if (!user) return false
    
    try {
      const userData = JSON.parse(user)
      const now = Date.now() / 1000
      
      // 检查token是否过期
      if (userData.expiresAt && userData.expiresAt < now) {
        localStorage.removeItem('user')
        return false
      }
      
      return !!userData.token
    } catch (error) {
      localStorage.removeItem('user')
      return false
    }
  },
  
  // 获取当前用户信息
  getCurrentUser: () => {
    const user = localStorage.getItem('user')
    if (!user) return null
    
    try {
      return JSON.parse(user)
    } catch (error) {
      localStorage.removeItem('user')
      return null
    }
  },
  
  // 清除用户信息
  clearUser: () => {
    localStorage.removeItem('user')
  },
  
  // 设置用户信息
  setUser: (userData) => {
    localStorage.setItem('user', JSON.stringify(userData))
  }
}

export default api
