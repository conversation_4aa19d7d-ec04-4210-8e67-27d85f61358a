package config

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
)

// Config 系统配置结构
type Config struct {
	// 服务器配置
	Server ServerConfig `json:"server"`
	// 数据库配置
	Database DatabaseConfig `json:"database"`
	// 认证配置
	Auth AuthConfig `json:"auth"`
	// 系统配置
	System SystemConfig `json:"system"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port          int    `json:"port"`            // 服务端口
	Host          string `json:"host"`            // 服务主机
	StaticPath    string `json:"static_path"`     // 静态文件路径
	LegacyAPIPath string `json:"legacy_api_path"` // 兼容API路径
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Host     string `json:"host"`     // 数据库主机
	Port     int    `json:"port"`     // 数据库端口
	Username string `json:"username"` // 数据库用户名
	Password string `json:"password"` // 数据库密码
	Database string `json:"database"` // 数据库名称
	Charset  string `json:"charset"`  // 字符集
}

// AuthConfig 认证配置
type AuthConfig struct {
	Enabled     bool   `json:"enabled"`      // 是否启用认证
	Username    string `json:"username"`     // 管理员用户名
	Password    string `json:"password"`     // 管理员密码
	JWTSecret   string `json:"jwt_secret"`   // JWT密钥
	TokenExpiry int    `json:"token_expiry"` // Token过期时间(小时)
	SessionName string `json:"session_name"` // Session名称
}

// SystemConfig 系统配置
type SystemConfig struct {
	AppName        string   `json:"app_name"`        // 应用名称
	Version        string   `json:"version"`         // 版本号
	Debug          bool     `json:"debug"`           // 调试模式
	DefaultProject string   `json:"default_project"` // 默认项目ID
	MaxFileSize    int64    `json:"max_file_size"`   // 最大文件大小(MB)
	AllowedOrigins []string `json:"allowed_origins"` // 允许的跨域来源
}

var (
	// GlobalConfig 全局配置实例
	GlobalConfig *Config
)

// LoadConfig 加载配置文件
func LoadConfig(configPath string) error {
	// 如果配置文件不存在，创建默认配置
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		fmt.Printf("配置文件不存在，创建默认配置: %s\n", configPath)
		if err := CreateDefaultConfig(configPath); err != nil {
			return fmt.Errorf("创建默认配置失败: %v", err)
		}
	}

	// 读取配置文件
	data, err := ioutil.ReadFile(configPath)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %v", err)
	}

	// 解析配置
	config := &Config{}
	if err := json.Unmarshal(data, config); err != nil {
		return fmt.Errorf("解析配置文件失败: %v", err)
	}

	// 验证配置
	if err := validateConfig(config); err != nil {
		return fmt.Errorf("配置验证失败: %v", err)
	}

	GlobalConfig = config
	fmt.Printf("配置加载成功: %s\n", configPath)
	return nil
}

// CreateDefaultConfig 创建默认配置文件
func CreateDefaultConfig(configPath string) error {
	defaultConfig := &Config{
		Server: ServerConfig{
			Port:          18082,
			Host:          "0.0.0.0",
			StaticPath:    "./web/dist",
			LegacyAPIPath: "/clouddata",
		},
		Database: DatabaseConfig{
			Host:     "localhost",
			Port:     3306,
			Username: "root",
			Password: "123456",
			Database: "cloud_data_manager",
			Charset:  "utf8mb4",
		},
		Auth: AuthConfig{
			Enabled:     true,
			Username:    "admin",
			Password:    "admin123",
			JWTSecret:   "your-secret-key-change-this-in-production",
			TokenExpiry: 24,
			SessionName: "cloud_data_session",
		},
		System: SystemConfig{
			AppName:        "云数据管理系统",
			Version:        "1.0.0",
			Debug:          false,
			DefaultProject: "zmxy",
			MaxFileSize:    10,
			AllowedOrigins: []string{"*"},
		},
	}

	// 创建配置目录
	if err := os.MkdirAll("config", 0755); err != nil {
		return err
	}

	// 序列化配置
	data, err := json.MarshalIndent(defaultConfig, "", "  ")
	if err != nil {
		return err
	}

	// 写入文件
	return ioutil.WriteFile(configPath, data, 0644)
}

// validateConfig 验证配置
func validateConfig(config *Config) error {
	if config.Server.Port <= 0 || config.Server.Port > 65535 {
		return fmt.Errorf("无效的服务端口: %d", config.Server.Port)
	}

	if config.Database.Host == "" {
		return fmt.Errorf("数据库主机不能为空")
	}

	if config.Auth.Enabled {
		if config.Auth.Username == "" || config.Auth.Password == "" {
			return fmt.Errorf("启用认证时，用户名和密码不能为空")
		}
		if config.Auth.JWTSecret == "" {
			return fmt.Errorf("JWT密钥不能为空")
		}
	}

	return nil
}

// GetDSN 获取数据库连接字符串
func (c *Config) GetDSN() string {
	return fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=True&loc=Local",
		c.Database.Username,
		c.Database.Password,
		c.Database.Host,
		c.Database.Port,
		c.Database.Database,
		c.Database.Charset,
	)
}

// IsAuthEnabled 检查是否启用认证
func (c *Config) IsAuthEnabled() bool {
	return c.Auth.Enabled
}

// GetServerAddr 获取服务器地址
func (c *Config) GetServerAddr() string {
	return fmt.Sprintf("%s:%d", c.Server.Host, c.Server.Port)
}
