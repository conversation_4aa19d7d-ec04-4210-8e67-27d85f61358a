package controllers

import (
	"cloud-data-manager/models"
	"cloud-data-manager/services"
	"net/http"

	"github.com/gin-gonic/gin"
)

// ProjectController 项目控制器
type ProjectController struct {
	projectService *services.ProjectService
}

// NewProjectController 创建项目控制器实例
func NewProjectController() *ProjectController {
	return &ProjectController{
		projectService: services.NewProjectService(),
	}
}

// GetAllProjects 获取所有项目
func (c *ProjectController) GetAllProjects(ctx *gin.Context) {
	projects, err := c.projectService.GetAllProjects()
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"data": projects})
}

// GetProject 获取指定项目
func (c *ProjectController) GetProject(ctx *gin.Context) {
	id := ctx.Param("projectId")

	project, err := c.projectService.GetProjectByID(id)
	if err != nil {
		if err.Error() == "project not found" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": "Project not found"})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"data": project})
}

// CreateProject 创建项目
func (c *ProjectController) CreateProject(ctx *gin.Context) {
	var req models.CreateProjectRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	err := c.projectService.CreateProject(req)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusCreated, gin.H{"message": "Project created successfully"})
}

// UpdateProject 更新项目
func (c *ProjectController) UpdateProject(ctx *gin.Context) {
	id := ctx.Param("projectId")

	var req models.UpdateProjectRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	err := c.projectService.UpdateProject(id, req)
	if err != nil {
		if err.Error() == "project not found" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": "Project not found"})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"message": "Project updated successfully"})
}

// DeleteProject 删除项目
func (c *ProjectController) DeleteProject(ctx *gin.Context) {
	id := ctx.Param("projectId")

	err := c.projectService.DeleteProject(id)
	if err != nil {
		if err.Error() == "project not found" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": "Project not found"})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"message": "Project deleted successfully"})
}
