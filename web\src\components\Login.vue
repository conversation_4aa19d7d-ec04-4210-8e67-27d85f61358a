<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <h2>{{ appName }}</h2>
        <p>请登录以继续</p>
      </div>
      
      <a-form
        :model="loginForm"
        :rules="rules"
        @finish="handleLogin"
        @finishFailed="handleLoginFailed"
        layout="vertical"
        class="login-form"
      >
        <a-form-item label="用户名" name="username">
          <a-input
            v-model:value="loginForm.username"
            placeholder="请输入用户名"
            size="large"
            :prefix="h(UserOutlined)"
          />
        </a-form-item>
        
        <a-form-item label="密码" name="password">
          <a-input-password
            v-model:value="loginForm.password"
            placeholder="请输入密码"
            size="large"
            :prefix="h(LockOutlined)"
            @pressEnter="handleLogin"
          />
        </a-form-item>
        
        <a-form-item>
          <a-button
            type="primary"
            html-type="submit"
            size="large"
            :loading="loading"
            block
          >
            登录
          </a-button>
        </a-form-item>
      </a-form>
      
      <div class="login-footer">
        <p>云数据管理系统 v1.0.0</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, h } from 'vue'
import { message } from 'ant-design-vue'
import { UserOutlined, LockOutlined } from '@ant-design/icons-vue'
import { authAPI } from '../api/auth'

const emit = defineEmits(['login-success'])

// 应用名称
const appName = ref('云数据管理系统')

// 表单数据
const loginForm = ref({
  username: '',
  password: ''
})

// 加载状态
const loading = ref(false)

// 表单验证规则
const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少6位', trigger: 'blur' }
  ]
}

// 处理登录
const handleLogin = async () => {
  try {
    loading.value = true
    
    const response = await authAPI.login({
      username: loginForm.value.username,
      password: loginForm.value.password
    })
    
    if (response.data) {
      message.success('登录成功')
      
      // 存储用户信息到localStorage
      localStorage.setItem('user', JSON.stringify({
        username: response.data.username,
        token: response.data.token,
        expiresAt: response.data.expires_at
      }))
      
      // 触发登录成功事件
      emit('login-success', response.data)
    }
  } catch (error) {
    console.error('登录失败:', error)
    
    if (error.response?.data?.error) {
      message.error(error.response.data.error)
    } else {
      message.error('登录失败，请检查网络连接')
    }
  } finally {
    loading.value = false
  }
}

// 处理登录失败
const handleLoginFailed = (errorInfo) => {
  console.log('表单验证失败:', errorInfo)
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-box {
  width: 100%;
  max-width: 400px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 40px;
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-header h2 {
  color: #1890ff;
  margin-bottom: 8px;
  font-size: 24px;
  font-weight: 600;
}

.login-header p {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.login-form {
  margin-bottom: 24px;
}

.login-footer {
  text-align: center;
  color: #999;
  font-size: 12px;
}

.login-footer p {
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-container {
    padding: 16px;
  }
  
  .login-box {
    padding: 24px;
  }
  
  .login-header h2 {
    font-size: 20px;
  }
}
</style>
