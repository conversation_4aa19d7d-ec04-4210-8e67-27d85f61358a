<template>
  <div class="project-management">
    <div class="page-header">
      <h2 class="page-title">项目管理</h2>
      <a-button type="primary" @click="openModal()" class="create-btn">
        <template #icon>
          <plus-outlined />
        </template>
        <span class="btn-text">新建项目</span>
      </a-button>
    </div>

    <!-- 项目列表 -->
    <div class="table-container">
      <a-table
        :columns="columns"
        :data-source="projects"
        :loading="loading"
        row-key="id"
        :pagination="{ pageSize: 10, showSizeChanger: true, showQuickJumper: true }"
        :scroll="{ x: 1000 }"
        class="projects-table"
      >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'name'">
          <div style="word-break: break-word; white-space: normal;">
            {{ record.name }}
          </div>
        </template>
        <template v-else-if="column.key === 'description'">
          <div style="word-break: break-word; white-space: normal; max-width: 200px;">
            {{ record.description || '-' }}
          </div>
        </template>
        <template v-else-if="column.key === 'created_at'">
          {{ formatDate(record.created_at) }}
        </template>
        <template v-else-if="column.key === 'updated_at'">
          {{ formatDate(record.updated_at) }}
        </template>
        <template v-else-if="column.key === 'aes_key'">
          <div style="font-family: monospace; word-break: break-all; max-width: 150px;">
            {{ record.aes_key || '-' }}
          </div>
        </template>

        <template v-else-if="column.key === 'action'">
          <a-space>
            <a-button size="small" @click="openModal(record)">编辑</a-button>
            <a-popconfirm
              title="确定要删除这个项目吗？"
              @confirm="deleteProject(record.id)"
            >
              <a-button size="small" danger>删除</a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </template>
    </a-table>
    </div>

    <!-- 新建/编辑项目模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="editingProject ? '编辑项目' : '新建项目'"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form
        ref="formRef"
        :model="form"
        :rules="rules"
        layout="vertical"
      >
        <a-form-item label="项目ID" name="id">
          <a-input
            v-model:value="form.id"
            placeholder="请输入项目ID"
            :disabled="!!editingProject"
          />
        </a-form-item>
        <a-form-item label="项目名称" name="name">
          <a-input v-model:value="form.name" placeholder="请输入项目名称" />
        </a-form-item>
        <a-form-item label="项目描述" name="description">
          <a-textarea
            v-model:value="form.description"
            placeholder="请输入项目描述"
            :rows="4"
          />
        </a-form-item>
        <a-form-item label="AES加密密钥 [已更新]" name="aes_key">
          <a-input
            v-model:value="form.aes_key"
            placeholder="请输入AES密钥（留空使用默认密钥）"
            style="font-family: monospace;"
          />
          <div style="font-size: 12px; color: #666; margin-top: 4px;">
            用于数据加密的AES密钥，每个项目可以设置独立的密钥 [版本2025-07-29]
          </div>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { projectAPI } from '../services/api'

const emit = defineEmits(['refresh'])

// 响应式数据
const projects = ref([])
const loading = ref(false)
const modalVisible = ref(false)
const editingProject = ref(null)
const formRef = ref()

// 表单数据
const form = ref({
  id: '',
  name: '',
  description: '',
  aes_key: ''
})

// 表单验证规则
const rules = {
  id: [
    { required: true, message: '请输入项目ID' },
    { pattern: /^[a-zA-Z0-9_-]+$/, message: '项目ID只能包含字母、数字、下划线和横线' }
  ],
  name: [
    { required: true, message: '请输入项目名称' }
  ],
  aes_key: [
    { max: 32, message: 'AES密钥长度不能超过32个字符' }
  ]
}

// 表格列定义
const columns = [
  {
    title: '项目ID',
    dataIndex: 'id',
    key: 'id',
    width: 120,
    fixed: 'left'
  },
  {
    title: '项目名称',
    dataIndex: 'name',
    key: 'name',
    width: 150
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    width: 200
  },
  {
    title: 'AES密钥',
    dataIndex: 'aes_key',
    key: 'aes_key',
    width: 180
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 160
  },
  {
    title: '更新时间',
    dataIndex: 'updated_at',
    key: 'updated_at',
    width: 160
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
    fixed: 'right'
  }
]

// 加载项目列表
const loadProjects = async () => {
  try {
    loading.value = true
    const response = await projectAPI.getAll()
    projects.value = response.data || []
  } catch (error) {
    message.error('加载项目列表失败')
    console.error('Failed to load projects:', error)
  } finally {
    loading.value = false
  }
}

// 打开模态框
const openModal = (project = null) => {
  editingProject.value = project
  if (project) {
    form.value = { 
      ...project
    }
  } else {
    form.value = {
      id: '',
      name: '',
      description: '',
      aes_key: ''
    }
  }
  modalVisible.value = true
}

// 处理表单提交
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    if (editingProject.value) {
      await projectAPI.update(editingProject.value.id, form.value)
      message.success('项目更新成功')
    } else {
      await projectAPI.create(form.value)
      message.success('项目创建成功')
    }
    
    modalVisible.value = false
    loadProjects()
    emit('refresh')
  } catch (error) {
    if (error.errorFields) {
      // 表单验证错误
      return
    }
    message.error(editingProject.value ? '项目更新失败' : '项目创建失败')
    console.error('Failed to save project:', error)
  }
}

// 取消操作
const handleCancel = () => {
  modalVisible.value = false
  editingProject.value = null
}

// 删除项目
const deleteProject = async (id) => {
  try {
    await projectAPI.delete(id)
    message.success('项目删除成功')
    loadProjects()
    emit('refresh')
  } catch (error) {
    console.error('Failed to delete project:', error)
    if (error.response && error.response.data && error.response.data.error) {
      message.error(`项目删除失败: ${error.response.data.error}`)
    } else {
      message.error('项目删除失败，请稍后重试')
    }
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 组件挂载时加载数据
onMounted(() => {
  loadProjects()
})
</script>

<style scoped>
.project-management {
  width: 100%;
  max-width: 100%;
}

.page-header {
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #262626;
}

.create-btn {
  flex-shrink: 0;
}

.table-container {
  width: 100%;
  overflow-x: auto;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.projects-table {
  width: 100%;
}

.projects-table :deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
  color: #262626;
}

.projects-table :deep(.ant-table-tbody > tr:hover > td) {
  background: #f5f5f5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .page-title {
    font-size: 20px;
    text-align: center;
  }

  .create-btn {
    width: 100%;
  }

  .btn-text {
    margin-left: 4px;
  }

  .projects-table :deep(.ant-table-thead > tr > th) {
    padding: 8px 4px;
    font-size: 12px;
  }

  .projects-table :deep(.ant-table-tbody > tr > td) {
    padding: 8px 4px;
    font-size: 12px;
  }
}

@media (max-width: 576px) {
  .page-title {
    font-size: 18px;
  }

  .projects-table :deep(.ant-table-thead > tr > th) {
    padding: 6px 2px;
    font-size: 11px;
  }

  .projects-table :deep(.ant-table-tbody > tr > td) {
    padding: 6px 2px;
    font-size: 11px;
  }

  .projects-table :deep(.ant-btn) {
    padding: 2px 6px;
    font-size: 11px;
    height: auto;
  }
}

/* 表格内容自适应 */
.projects-table :deep(.ant-table-cell) {
  word-break: break-word;
  white-space: normal;
}

.projects-table :deep(.ant-table-pagination) {
  margin: 16px 0;
  text-align: center;
}

@media (max-width: 768px) {
  .projects-table :deep(.ant-table-pagination) {
    margin: 12px 0;
  }

  .projects-table :deep(.ant-pagination-options) {
    display: none;
  }
}
</style>
