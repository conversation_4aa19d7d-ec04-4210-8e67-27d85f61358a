import axios from 'axios'

// 创建axios实例
const api = axios.create({
  baseURL: 'http://localhost:18082/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 从localStorage获取token
    const user = localStorage.getItem('user')
    if (user) {
      try {
        const userData = JSON.parse(user)
        if (userData.token) {
          config.headers.Authorization = `Bearer ${userData.token}`
        }
      } catch (error) {
        console.error('解析用户数据失败:', error)
        localStorage.removeItem('user')
      }
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    console.error('API Error:', error)
    if (error.response?.status === 401) {
      // 未授权，清除本地存储的用户信息
      localStorage.removeItem('user')

      // 如果不是登录页面且不是检查认证状态的请求，才重新加载页面
      if (window.location.pathname !== '/login' &&
          !error.config?.url?.includes('/auth/check')) {
        window.location.reload()
      }
    }
    return Promise.reject(error)
  }
)

// 项目API
export const projectAPI = {
  // 获取所有项目
  getAll() {
    return api.get('/projects')
  },
  
  // 获取单个项目
  getById(id) {
    return api.get(`/projects/${id}`)
  },
  
  // 创建项目
  create(data) {
    return api.post('/projects', data)
  },
  
  // 更新项目
  update(id, data) {
    return api.put(`/projects/${id}`, data)
  },
  
  // 删除项目
  delete(id) {
    return api.delete(`/projects/${id}`)
  }
}

// 数据表API
export const dataTableAPI = {
  // 获取项目的所有数据表
  getByProject(projectId) {
    return api.get(`/projects/${projectId}/tables`)
  },
  
  // 创建数据表
  create(projectId, data) {
    return api.post(`/projects/${projectId}/tables`, data)
  },
  
  // 更新数据表
  update(projectId, tableName, data) {
    return api.put(`/projects/${projectId}/tables/${tableName}`, data)
  },
  
  // 删除数据表
  delete(projectId, tableName) {
    return api.delete(`/projects/${projectId}/tables/${tableName}`)
  }
}

// 数据项API
export const dataItemAPI = {
  // 获取数据表的所有数据项
  getByTable(projectId, tableName) {
    return api.get(`/projects/${projectId}/tables/${tableName}/items`)
  },
  
  // 创建数据项
  create(projectId, tableName, data) {
    return api.post(`/projects/${projectId}/tables/${tableName}/items`, data)
  },
  
  // 更新数据项
  update(projectId, tableName, itemKey, data) {
    return api.put(`/projects/${projectId}/tables/${tableName}/items/${itemKey}`, data)
  },
  
  // 删除数据项
  delete(projectId, tableName, itemKey) {
    return api.delete(`/projects/${projectId}/tables/${tableName}/items/${itemKey}`)
  }
}

// Lua数据API
export const luaAPI = {
  // 获取Lua数据预览（无需权限验证，返回JSON格式）
  getLuaData(projectId) {
    return api.get(`/projects/${projectId}/preview`)
  }
}

export default api
