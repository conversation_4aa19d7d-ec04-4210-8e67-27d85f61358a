<template>
  <div>
    <div style="margin-bottom: 16px; display: flex; justify-content: space-between; align-items: center">
      <h3>{{ tableInfo.name }} - 数据项</h3>
      <a-button type="primary" @click="openModal()">
        <template #icon>
          <plus-outlined />
        </template>
        新建数据项
      </a-button>
    </div>

    <!-- 数据项列表 -->
    <a-list
      :data-source="items"
      :loading="loading"
      item-layout="vertical"
      :pagination="{ pageSize: 10 }"
    >
      <template #renderItem="{ item }">
        <a-list-item>
          <template #actions>
            <a-button size="small" @click="openModal(item)">编辑</a-button>
            <a-popconfirm
              title="确定要删除这个数据项吗？"
              @confirm="deleteItem(item.key)"
            >
              <a-button size="small" danger>删除</a-button>
            </a-popconfirm>
          </template>
          
          <a-list-item-meta>
            <template #title>
              <span style="font-weight: bold">{{ item.key }}</span>
              <a-tag :color="getTypeColor(item.type)" style="margin-left: 8px">
                {{ item.type }}
              </a-tag>
            </template>
            <template #description>
              <div style="margin-top: 8px">
                <strong>值:</strong>
                <pre style="background: #f5f5f5; padding: 8px; margin: 4px 0; border-radius: 4px; white-space: pre-wrap">{{ formatValue(item.value) }}</pre>
              </div>
            </template>
          </a-list-item-meta>
        </a-list-item>
      </template>
    </a-list>

    <!-- 新建/编辑数据项模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="editingItem ? '编辑数据项' : '新建数据项'"
      @ok="handleSubmit"
      @cancel="handleCancel"
      width="600px"
    >
      <a-form
        ref="formRef"
        :model="form"
        :rules="rules"
        layout="vertical"
      >
        <a-form-item label="键名" name="key">
          <a-input
            v-model:value="form.key"
            placeholder="请输入键名"
            :disabled="!!editingItem"
          />
        </a-form-item>
        <a-form-item label="数据类型" name="type">
          <a-select v-model:value="form.type" placeholder="请选择数据类型">
            <a-select-option value="string">字符串</a-select-option>
            <a-select-option value="number">数字</a-select-option>
            <a-select-option value="boolean">布尔值</a-select-option>
            <a-select-option value="array">数组</a-select-option>
            <a-select-option value="object">对象</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="值" name="value">
          <a-textarea
            v-model:value="form.value"
            placeholder="请输入值，数组支持 [1,2,&quot;3&quot;] 或 {1,2,&quot;3&quot;} 格式"
            :rows="6"
          />
          <div style="margin-top: 4px; color: #666; font-size: 12px">
            数组支持JSON格式 [1,2,"3"] 或Lua格式 {1,2,"3"}，对象使用JSON格式 {"key": "value"}
          </div>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { dataItemAPI } from '../services/api'

const props = defineProps({
  projectId: {
    type: String,
    required: true
  },
  tableName: {
    type: String,
    required: true
  },
  tableInfo: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['refresh'])

// 响应式数据
const items = ref([])
const loading = ref(false)
const modalVisible = ref(false)
const editingItem = ref(null)
const formRef = ref()

// 表单数据
const form = ref({
  key: '',
  type: 'string',
  value: ''
})

// 表单验证规则
const rules = {
  key: [
    { required: true, message: '请输入键名' },
    { pattern: /^[a-zA-Z0-9_\u4e00-\u9fa5]+$/, message: '键名只能包含字母、数字、下划线和中文' }
  ],
  type: [
    { required: true, message: '请选择数据类型' }
  ],
  value: [
    { required: true, message: '请输入值' }
  ]
}

// 加载数据项列表
const loadItems = async () => {
  if (!props.projectId || !props.tableName) return

  try {
    loading.value = true
    const response = await dataItemAPI.getByTable(props.projectId, props.tableName)
    // 转换后端字段名以匹配前端期望
    items.value = (response.data || []).map(item => ({
      ...item,
      key: item.item_key,
      value: item.item_value,
      type: item.value_type
    }))
  } catch (error) {
    message.error('加载数据项列表失败')
    console.error('Failed to load items:', error)
  } finally {
    loading.value = false
  }
}

// 打开模态框
const openModal = (item = null) => {
  editingItem.value = item
  if (item) {
    form.value = {
      key: item.key,
      type: item.type,
      value: formatValue(item.value)
    }
  } else {
    form.value = {
      key: '',
      type: 'string',
      value: ''
    }
  }
  modalVisible.value = true
}

// 处理表单提交
const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    // 根据数据类型转换值
    let processedValue = form.value.value

    try {
      if (form.value.type === 'array') {
        // 对于数组类型，支持Lua格式 {1,2,"3"} 和JSON格式 [1,2,"3"]
        let arrayStr = form.value.value.trim()

        // 如果是Lua格式 {1,2,"3"}，转换为JSON格式 [1,2,"3"]
        if (arrayStr.startsWith('{') && arrayStr.endsWith('}')) {
          arrayStr = '[' + arrayStr.slice(1, -1) + ']'
        }

        processedValue = JSON.parse(arrayStr)
      } else if (form.value.type === 'object') {
        // 对于对象类型，尝试解析JSON
        processedValue = JSON.parse(form.value.value)
      } else if (form.value.type === 'number') {
        // 对于数字类型，转换为数字
        processedValue = Number(form.value.value)
      } else if (form.value.type === 'boolean') {
        // 对于布尔类型，转换为布尔值
        processedValue = form.value.value === 'true' || form.value.value === true
      }
      // 字符串类型保持原样
    } catch (parseError) {
      let errorMsg = '值格式不正确，请检查格式。[已修复版本] '
      if (form.value.type === 'array') {
        errorMsg += '数组支持 [1,2,"3"] 或 {1,2,"3"} 格式'
      } else if (form.value.type === 'object') {
        errorMsg += '对象支持 {"key":"value"} 格式'
      } else {
        errorMsg += '请输入有效的JSON格式'
      }
      message.error(errorMsg)
      return
    }

    // 转换字段名以匹配后端API
    const data = {
      item_key: form.value.key,
      value_type: form.value.type,
      item_value: processedValue
    }

    if (editingItem.value) {
      await dataItemAPI.update(props.projectId, props.tableName, editingItem.value.key, data)
      message.success('数据项更新成功')
    } else {
      await dataItemAPI.create(props.projectId, props.tableName, data)
      message.success('数据项创建成功')
    }

    modalVisible.value = false
    loadItems()
  } catch (error) {
    if (error.errorFields) {
      // 表单验证错误
      return
    }
    console.error('Failed to save item:', error)
    if (error.response && error.response.data && error.response.data.error) {
      message.error(`操作失败: ${error.response.data.error}`)
    } else {
      message.error(editingItem.value ? '数据项更新失败' : '数据项创建失败')
    }
  }
}

// 取消操作
const handleCancel = () => {
  modalVisible.value = false
  editingItem.value = null
}

// 删除数据项
const deleteItem = async (key) => {
  try {
    await dataItemAPI.delete(props.projectId, props.tableName, key)
    message.success('数据项删除成功')
    loadItems()
  } catch (error) {
    message.error('数据项删除失败')
    console.error('Failed to delete item:', error)
  }
}

// 格式化值显示
const formatValue = (value) => {
  if (typeof value === 'string') {
    try {
      // 尝试解析JSON字符串
      const parsed = JSON.parse(value)
      // 如果解析成功，格式化显示
      if (typeof parsed === 'object') {
        return JSON.stringify(parsed, null, 2)
      } else {
        // 如果是基本类型，直接返回解析后的值
        return String(parsed)
      }
    } catch (e) {
      // 如果不是有效的JSON，直接返回原字符串
      return value
    }
  }
  return JSON.stringify(value, null, 2)
}

// 获取类型颜色
const getTypeColor = (type) => {
  const colors = {
    string: 'blue',
    number: 'green',
    boolean: 'orange',
    array: 'purple',
    object: 'red'
  }
  return colors[type] || 'default'
}

// 监听属性变化
watch([() => props.projectId, () => props.tableName], () => {
  if (props.projectId && props.tableName) {
    loadItems()
  }
}, { immediate: true })

// 组件挂载时加载数据
onMounted(() => {
  if (props.projectId && props.tableName) {
    loadItems()
  }
})
</script>
