# 云数据管理系统 - 问题修复总结

## 修复的问题

### 1. 登录状态持久化问题
**问题描述**: 当前项目刷新之后就需要重新登陆

**修复内容**:
- 修改了 `web/src/App.vue` 中的 `checkAuthStatus` 函数
- 添加了本地存储token的检查和验证逻辑
- 实现了页面刷新后自动恢复登录状态
- 优化了token过期处理机制

**技术细节**:
- 在应用初始化时检查 localStorage 中的用户信息
- 验证token的有效期，如果未过期则直接设置为已认证状态
- 异步验证服务器端token有效性
- 改进了响应拦截器，避免在认证检查时触发页面重载

### 2. 原始数据API重构
**问题描述**: 重写原始数据api的逻辑，把原始数据api常开，但是请求时需要携带秘钥，秘钥在ui端设置

**修复内容**:
- 创建了新的常开原始数据API端点 `/api/raw/:projectId`
- 实现了基于项目AES密钥的API访问验证
- 保留了原有的调试API功能以确保向后兼容
- 更新了前端界面以支持新的API功能

**技术细节**:

#### 后端修改:
1. **新增控制器方法** (`controllers/lua_controller.go`):
   - `GetRawData`: 新的常开API方法
   - 使用项目的AES密钥作为API访问密钥
   - 支持请求头 `X-API-Key` 和URL参数 `api_key` 两种验证方式
   - 返回纯文本格式的Lua数据

2. **认证中间件更新** (`middleware/auth.go`):
   - 添加了 `/api/raw/` 路径的认证跳过规则
   - 新的API不需要JWT认证，通过API密钥验证

3. **路由配置** (`server.go`):
   - 添加了新的路由 `GET /api/raw/:projectId`

#### 前端修改:
1. **API服务更新** (`web/src/services/api.js`):
   - 添加了 `getRawDataWithApiKey` 方法
   - 支持通过API密钥调用新的常开API

2. **LuaExport组件更新** (`web/src/components/LuaExport.vue`):
   - 添加了常开API的展示和测试功能
   - 显示API地址、密钥和使用说明
   - 提供API调用示例复制功能
   - 保留了原有的调试API功能

3. **新增API测试页面** (`web/src/components/ApiTest.vue`):
   - 提供完整的API测试界面
   - 支持自定义项目ID和API密钥测试
   - 显示JavaScript、cURL、Python调用示例
   - 实时测试API功能和错误处理

## API使用说明

### 常开原始数据API
- **端点**: `GET /api/raw/:projectId`
- **认证**: 通过API密钥（项目的AES密钥）
- **请求方式**:
  - 请求头: `X-API-Key: 您的密钥`
  - URL参数: `?api_key=您的密钥`
- **返回格式**: 纯文本格式的Lua数据
- **特点**: 常开，无需启用调试模式

### 调用示例

#### JavaScript
```javascript
fetch('http://localhost:18082/api/raw/test', {
  headers: {
    'X-API-Key': 'testtesttesttest'
  }
})
.then(response => response.text())
.then(data => console.log(data))
```

#### cURL
```bash
# 请求头方式
curl -H "X-API-Key: testtesttesttest" "http://localhost:18082/api/raw/test"

# URL参数方式
curl "http://localhost:18082/api/raw/test?api_key=testtesttesttest"
```

#### Python
```python
import requests

headers = {'X-API-Key': 'testtesttesttest'}
response = requests.get('http://localhost:18082/api/raw/test', headers=headers)
print(response.text)
```

## 测试验证

### 1. 登录状态持久化测试
- ✅ 登录后刷新页面，登录状态保持
- ✅ Token过期后自动清除本地存储
- ✅ 服务器端token验证失败时正确处理

### 2. 常开API测试
- ✅ 正确的API密钥可以成功访问
- ✅ 错误的API密钥返回401错误
- ✅ 缺少API密钥返回相应错误信息
- ✅ 支持请求头和URL参数两种传递方式
- ✅ 返回正确的Lua格式数据

## 安全考虑

1. **API密钥验证**: 使用项目的AES密钥作为API访问密钥，确保只有知道密钥的用户才能访问
2. **认证跳过**: 新API跳过JWT认证但有自己的密钥验证机制
3. **错误处理**: 提供清晰的错误信息，便于调试但不泄露敏感信息
4. **向后兼容**: 保留原有的调试API功能，不影响现有用户

## 部署说明

1. 重新启动后端服务以加载新的API端点
2. 前端会自动加载新的功能，无需额外配置
3. 现有的调试API功能继续正常工作
4. 新的常开API立即可用，使用项目的AES密钥进行访问

## 文件修改清单

### 后端文件:
- `controllers/lua_controller.go` - 添加新的GetRawData方法
- `middleware/auth.go` - 更新认证跳过规则
- `server.go` - 添加新的API路由

### 前端文件:
- `web/src/App.vue` - 修复登录状态持久化，添加API测试菜单
- `web/src/services/api.js` - 添加新的API调用方法，优化响应拦截器
- `web/src/components/LuaExport.vue` - 添加常开API功能展示
- `web/src/components/ApiTest.vue` - 新增API测试页面

所有修改都已测试验证，功能正常工作。
