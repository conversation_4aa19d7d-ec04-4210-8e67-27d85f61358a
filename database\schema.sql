-- 云数据管理系统数据库结构
-- 支持多项目数据隔离

-- 项目表
CREATE TABLE IF NOT EXISTS projects (
    id VARCHAR(50) PRIMARY KEY,           -- 项目ID (如 zmxy)
    name VARCHAR(100) NOT NULL,           -- 项目名称 (用户友好的名称)
    description TEXT,                     -- 项目描述
    aes_key VARCHAR(32),                  -- AES加密密钥
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 数据表 (存储项目中的表，如 tuse, node)
CREATE TABLE IF NOT EXISTS data_tables (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_id VARCHAR(50) NOT NULL,      -- 关联的项目ID
    table_name VARCHAR(100) NOT NULL,     -- 表名 (如 tuse, node)
    description TEXT,                     -- 表描述
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    UNIQUE KEY unique_project_table (project_id, table_name)
);

-- 数据项表 (存储具体的键值对数据)
CREATE TABLE IF NOT EXISTS data_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_id VARCHAR(50) NOT NULL,      -- 关联的项目ID
    table_name VARCHAR(100) NOT NULL,     -- 表名
    item_key VARCHAR(200) NOT NULL,       -- 数据项的键 (如 "服务器", "公告叉叉")
    item_value TEXT,                      -- 数据项的值 (JSON格式存储数组或对象)
    value_type ENUM('array', 'object', 'string', 'number', 'boolean') DEFAULT 'array',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    UNIQUE KEY unique_project_table_key (project_id, table_name, item_key),
    INDEX idx_project_table (project_id, table_name)
);

-- 插入默认项目数据
INSERT INTO projects (id, name, description) VALUES 
('zmxy', '造梦西游', '造梦西游游戏数据配置项目')
ON DUPLICATE KEY UPDATE name = VALUES(name);

-- 插入默认数据表
INSERT INTO data_tables (project_id, table_name, description) VALUES 
('zmxy', 'tuse', '图色数据表'),
('zmxy', 'node', '节点数据表')
ON DUPLICATE KEY UPDATE description = VALUES(description);
