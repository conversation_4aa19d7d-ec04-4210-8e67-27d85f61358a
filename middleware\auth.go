package middleware

import (
	"cloud-data-manager/config"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v4"
)

// Claims JWT声明结构
type Claims struct {
	Username string `json:"username"`
	jwt.RegisteredClaims
}

// AuthMiddleware 认证中间件
func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 如果未启用认证，直接通过
		if !config.GlobalConfig.IsAuthEnabled() {
			c.Next()
			return
		}

		// 跳过OPTIONS请求（CORS预检）
		if c.Request.Method == "OPTIONS" {
			c.Next()
			return
		}

		// 跳过认证相关接口
		if c.Request.URL.Path == "/api/auth/login" ||
			c.Request.URL.Path == "/api/auth/check" {
			c.Next()
			return
		}

		// 跳过预览接口（无需认证）
		if strings.Contains(c.Request.URL.Path, "/preview") {
			c.Next()
			return
		}

		// 跳过静态文件
		if strings.HasPrefix(c.Request.URL.Path, "/assets/") ||
			c.Request.URL.Path == "/" ||
			c.Request.URL.Path == "/favicon.ico" {
			c.Next()
			return
		}

		// 获取Token
		tokenString := c.GetHeader("Authorization")
		if tokenString == "" {
			// 尝试从Cookie获取
			cookie, err := c.Cookie(config.GlobalConfig.Auth.SessionName)
			if err != nil {
				c.JSON(http.StatusUnauthorized, gin.H{
					"error": "未提供认证令牌",
					"code":  "UNAUTHORIZED",
				})
				c.Abort()
				return
			}
			tokenString = cookie
		} else {
			// 移除Bearer前缀
			if strings.HasPrefix(tokenString, "Bearer ") {
				tokenString = strings.TrimPrefix(tokenString, "Bearer ")
			}
		}

		// 验证Token
		claims, err := ValidateToken(tokenString)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "无效的认证令牌",
				"code":  "INVALID_TOKEN",
			})
			c.Abort()
			return
		}

		// 将用户信息存储到上下文
		c.Set("username", claims.Username)
		c.Next()
	}
}

// GenerateToken 生成JWT令牌
func GenerateToken(username string) (string, error) {
	claims := Claims{
		Username: username,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Duration(config.GlobalConfig.Auth.TokenExpiry) * time.Hour)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    config.GlobalConfig.System.AppName,
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(config.GlobalConfig.Auth.JWTSecret))
}

// ValidateToken 验证JWT令牌
func ValidateToken(tokenString string) (*Claims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(config.GlobalConfig.Auth.JWTSecret), nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(*Claims); ok && token.Valid {
		return claims, nil
	}

	return nil, jwt.ErrInvalidKey
}

// RefreshToken 刷新令牌
func RefreshToken(tokenString string) (string, error) {
	claims, err := ValidateToken(tokenString)
	if err != nil {
		return "", err
	}

	// 如果令牌还有超过1小时的有效期，不需要刷新
	if time.Until(claims.ExpiresAt.Time) > time.Hour {
		return tokenString, nil
	}

	// 生成新令牌
	return GenerateToken(claims.Username)
}
