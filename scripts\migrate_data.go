package main

import (
	"bufio"
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"regexp"
	"strconv"
	"strings"

	_ "github.com/go-sql-driver/mysql"
)

// 数据库连接
var db *sql.DB

// 初始化数据库连接
func initDB() error {
	dsn := os.Getenv("DATABASE_URL")
	if dsn == "" {
		dsn = "root:926481@tcp(127.0.0.1:3306)/cloud_data?charset=utf8mb4&parseTime=True&loc=Local"
	}

	var err error
	db, err = sql.Open("mysql", dsn)
	if err != nil {
		return fmt.Errorf("failed to open database: %v", err)
	}

	if err = db.Ping(); err != nil {
		return fmt.Errorf("failed to ping database: %v", err)
	}

	log.Println("Database connected successfully")
	return nil
}

// 解析Lua文件
func parseLuaFile(filename string) (map[string]map[string]interface{}, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to open file: %v", err)
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	var content strings.Builder

	for scanner.Scan() {
		content.WriteString(scanner.Text())
		content.WriteString("\n")
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("failed to read file: %v", err)
	}

	return parseLuaContent(content.String())
}

// 解析Lua内容
func parseLuaContent(content string) (map[string]map[string]interface{}, error) {
	result := make(map[string]map[string]interface{})

	// 查找项目定义 (如 zmxy = {...})
	projectRegex := regexp.MustCompile(`(\w+)\s*=\s*\{`)
	projectMatches := projectRegex.FindAllStringSubmatch(content, -1)

	if len(projectMatches) == 0 {
		return nil, fmt.Errorf("no project definition found")
	}

	projectID := projectMatches[0][1]
	log.Printf("Found project: %s", projectID)

	// 查找表定义 (如 tuse = {...})
	tableRegex := regexp.MustCompile(`(\w+)\s*=\s*\{([^}]*)\}`)
	tableMatches := tableRegex.FindAllStringSubmatch(content, -1)

	tables := make(map[string]interface{})

	for _, match := range tableMatches {
		tableName := match[1]
		if tableName == projectID {
			continue // 跳过项目定义本身
		}

		tableContent := match[2]
		log.Printf("Parsing table: %s", tableName)

		items := parseTableItems(tableContent)
		tables[tableName] = items
	}

	result[projectID] = tables
	return result, nil
}

// 解析表中的数据项
func parseTableItems(content string) map[string]interface{} {
	items := make(map[string]interface{})

	// 匹配键值对 (如 服务器 = {0 , 0 , 0 , 0 , "ffffff" , ...})
	itemRegex := regexp.MustCompile(`([a-zA-Z0-9_\p{Han}]+)\s*=\s*\{([^}]*)\}`)
	itemMatches := itemRegex.FindAllStringSubmatch(content, -1)

	for _, match := range itemMatches {
		key := strings.TrimSpace(match[1])
		valueContent := strings.TrimSpace(match[2])

		// 解析数组值
		values := parseArrayValues(valueContent)
		items[key] = values
	}

	return items
}

// 解析数组值
func parseArrayValues(content string) []interface{} {
	var values []interface{}

	// 分割值，处理逗号分隔
	parts := strings.Split(content, ",")

	for _, part := range parts {
		part = strings.TrimSpace(part)
		if part == "" {
			continue
		}

		// 移除引号
		if strings.HasPrefix(part, "\"") && strings.HasSuffix(part, "\"") {
			values = append(values, strings.Trim(part, "\""))
		} else if strings.HasPrefix(part, "'") && strings.HasSuffix(part, "'") {
			values = append(values, strings.Trim(part, "'"))
		} else {
			// 尝试解析为数字
			if num, err := strconv.ParseFloat(part, 64); err == nil {
				if num == float64(int64(num)) {
					values = append(values, int64(num))
				} else {
					values = append(values, num)
				}
			} else {
				values = append(values, part)
			}
		}
	}

	return values
}

// 迁移数据到数据库
func migrateData(data map[string]map[string]interface{}) error {
	for projectID, tables := range data {
		log.Printf("Migrating project: %s", projectID)

		// 确保项目存在
		_, err := db.Exec(`
			INSERT INTO projects (id, name, description) VALUES (?, ?, ?)
			ON DUPLICATE KEY UPDATE name = VALUES(name)
		`, projectID, projectID, fmt.Sprintf("从%s.lua迁移的项目", projectID))
		if err != nil {
			return fmt.Errorf("failed to insert project: %v", err)
		}

		for tableName, tableData := range tables {
			log.Printf("Migrating table: %s.%s", projectID, tableName)

			// 确保数据表存在
			_, err := db.Exec(`
				INSERT INTO data_tables (project_id, table_name, description) VALUES (?, ?, ?)
				ON DUPLICATE KEY UPDATE description = VALUES(description)
			`, projectID, tableName, fmt.Sprintf("%s数据表", tableName))
			if err != nil {
				return fmt.Errorf("failed to insert data table: %v", err)
			}

			// 迁移数据项
			if items, ok := tableData.(map[string]interface{}); ok {
				for itemKey, itemValue := range items {
					log.Printf("Migrating item: %s.%s.%s", projectID, tableName, itemKey)

					// 将值转换为JSON
					valueJSON, err := json.Marshal(itemValue)
					if err != nil {
						log.Printf("Failed to marshal value for %s: %v", itemKey, err)
						continue
					}

					// 检测值类型
					valueType := detectValueType(itemValue)

					// 插入或更新数据项
					_, err = db.Exec(`
						INSERT INTO data_items (project_id, table_name, item_key, item_value, value_type) 
						VALUES (?, ?, ?, ?, ?)
						ON DUPLICATE KEY UPDATE item_value = VALUES(item_value), value_type = VALUES(value_type)
					`, projectID, tableName, itemKey, string(valueJSON), valueType)
					if err != nil {
						log.Printf("Failed to insert data item %s: %v", itemKey, err)
						continue
					}
				}
			}
		}
	}

	return nil
}

// 检测值类型
func detectValueType(value interface{}) string {
	switch value.(type) {
	case []interface{}:
		return "array"
	case map[string]interface{}:
		return "object"
	case string:
		return "string"
	case int, int64, float64:
		return "number"
	case bool:
		return "boolean"
	default:
		return "string"
	}
}

func main() {
	// 初始化数据库
	if err := initDB(); err != nil {
		log.Fatal("Failed to initialize database:", err)
	}
	defer db.Close()

	// 检查参数.lua文件是否存在
	filename := "参数.lua"
	if len(os.Args) > 1 {
		filename = os.Args[1]
	}

	if _, err := os.Stat(filename); os.IsNotExist(err) {
		log.Fatalf("File %s does not exist", filename)
	}

	log.Printf("Starting migration from %s", filename)

	// 解析Lua文件
	data, err := parseLuaFile(filename)
	if err != nil {
		log.Fatal("Failed to parse Lua file:", err)
	}

	log.Printf("Parsed data structure: %+v", data)

	// 迁移数据
	if err := migrateData(data); err != nil {
		log.Fatal("Failed to migrate data:", err)
	}

	log.Println("Data migration completed successfully!")
}
