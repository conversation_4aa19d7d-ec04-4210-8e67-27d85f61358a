# 云数据管理系统

一个基于Go和React的多项目数据管理系统，支持Lua格式数据导出和AES加密，完全兼容原有API。

## 功能特性

- 🚀 **多项目管理**: 支持创建和管理多个独立项目
- 📊 **数据管理**: 灵活的数据表和数据项管理
- 🔒 **数据加密**: AES-128-CBC加密，兼容原有API
- 🌐 **Web界面**: 现代化的React管理界面
- 📤 **数据导出**: 支持原始和加密Lua格式导出
- 🔄 **数据迁移**: 自动从现有参数.lua文件迁移数据
- 🔌 **API兼容**: 完全兼容原有`/zmxycons`接口

## 系统架构

```
云数据管理系统
├── 后端服务 (Go + Gin)
│   ├── 项目管理
│   ├── 数据表管理
│   ├── 数据项管理
│   └── Lua数据生成
├── 前端界面 (React + Ant Design)
│   ├── 项目管理界面
│   ├── 数据管理界面
│   └── 数据导出界面
├── 数据库 (MySQL)
│   ├── projects (项目表)
│   ├── data_tables (数据表)
│   └── data_items (数据项)
└── 工具脚本
    ├── 数据迁移工具
    └── 安装脚本
```

## 快速开始

### 环境要求

- Go 1.19+
- Node.js 16+
- MySQL 5.7+

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd 云数据
   ```

2. **运行安装脚本**
   ```bash
   chmod +x scripts/setup.sh
   ./scripts/setup.sh
   ```

3. **配置数据库**
   
   创建MySQL数据库：
   ```sql
   CREATE DATABASE cloud_data CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

4. **迁移现有数据（可选）**
   
   如果有现有的`参数.lua`文件：
   ```bash
   ./migrate.sh
   ```

5. **启动服务**
   ```bash
   ./start.sh
   ```

### 手动安装

如果自动安装脚本失败，可以手动安装：

1. **安装后端依赖**
   ```bash
   go mod tidy
   ```

2. **构建后端**
   ```bash
   go build -o bin/server server.go
   go build -o bin/migrate_data scripts/migrate_data.go
   ```

3. **安装前端依赖**
   ```bash
   cd web
   npm install
   npm run build
   cd ..
   ```

4. **设置环境变量**
   ```bash
   export DATABASE_URL="root:password@tcp(localhost:3306)/cloud_data?charset=utf8mb4&parseTime=True&loc=Local"
   ```

5. **启动服务**
   ```bash
   ./bin/server
   ```

## 使用说明

### 访问地址

- **管理界面**: http://localhost:18082
- **API接口**: http://localhost:18082/api
- **兼容接口**: http://localhost:18082/zmxycons

### API接口

#### 项目管理
- `GET /api/projects` - 获取所有项目
- `POST /api/projects` - 创建项目
- `GET /api/projects/{id}` - 获取指定项目
- `PUT /api/projects/{id}` - 更新项目
- `DELETE /api/projects/{id}` - 删除项目

#### 数据表管理
- `GET /api/projects/{projectId}/tables` - 获取项目的数据表
- `POST /api/projects/{projectId}/tables` - 创建数据表
- `PUT /api/projects/{projectId}/tables/{tableName}` - 更新数据表
- `DELETE /api/projects/{projectId}/tables/{tableName}` - 删除数据表

#### 数据项管理
- `GET /api/projects/{projectId}/tables/{tableName}/items` - 获取数据项
- `POST /api/projects/{projectId}/tables/{tableName}/items` - 创建数据项
- `GET /api/projects/{projectId}/tables/{tableName}/items/{itemKey}` - 获取指定数据项
- `PUT /api/projects/{projectId}/tables/{tableName}/items/{itemKey}` - 更新数据项
- `DELETE /api/projects/{projectId}/tables/{tableName}/items/{itemKey}` - 删除数据项

#### 数据导出
- `GET /api/projects/{projectId}/lua` - 获取原始Lua数据
- `GET /zmxycons?project={projectId}` - 获取加密数据（兼容原API）

### 数据格式

#### 项目数据结构
```json
{
  "id": "zmxy",
  "name": "造梦西游",
  "description": "造梦西游项目数据",
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

#### 数据项结构
```json
{
  "item_key": "服务器",
  "item_value": [0, 0, 0, 0, "ffffff"],
  "value_type": "array"
}
```

#### Lua输出格式
```lua
zmxy = {
  tuse = {
    服务器 = {0, 0, 0, 0, "ffffff"},
    -- 其他数据项...
  },
  node = {
    -- 节点数据...
  }
}
```

## 数据迁移

系统提供自动数据迁移工具，可以从现有的`参数.lua`文件迁移数据：

```bash
# 迁移指定文件
./bin/migrate_data 参数.lua

# 或使用迁移脚本
./migrate.sh
```

迁移工具会：
1. 解析Lua文件结构
2. 自动创建项目和数据表
3. 导入所有数据项
4. 保持数据类型和结构

## 配置说明

### 环境变量

- `DATABASE_URL`: MySQL连接字符串
- `PORT`: 服务端口（默认18082）

### 数据库配置

默认连接字符串：
```
root:password@tcp(localhost:3306)/cloud_data?charset=utf8mb4&parseTime=True&loc=Local
```

## 开发说明

### 项目结构

```
.
├── controllers/          # 控制器
├── database/            # 数据库相关
├── models/              # 数据模型
├── services/            # 业务逻辑
├── scripts/             # 工具脚本
├── web/                 # 前端代码
│   ├── src/
│   │   ├── components/  # React组件
│   │   └── services/    # API服务
│   └── public/
├── server.go            # 主服务文件
├── go.mod              # Go依赖
└── README.md           # 说明文档
```

### 开发环境

1. **后端开发**
   ```bash
   go run server.go
   ```

2. **前端开发**
   ```bash
   cd web
   npm start
   ```

### 构建部署

1. **构建后端**
   ```bash
   go build -o bin/server server.go
   ```

2. **构建前端**
   ```bash
   cd web
   npm run build
   ```

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查MySQL服务是否启动
   - 验证连接字符串是否正确
   - 确认数据库用户权限

2. **前端无法访问API**
   - 检查后端服务是否启动
   - 验证端口是否被占用
   - 检查防火墙设置

3. **数据迁移失败**
   - 确认参数.lua文件格式正确
   - 检查数据库写入权限
   - 查看迁移日志输出

### 日志查看

服务启动后会输出详细日志，包括：
- 数据库连接状态
- API请求日志
- 错误信息

## 许可证

本项目采用 MIT 许可证。

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交Issue
- 发送邮件
