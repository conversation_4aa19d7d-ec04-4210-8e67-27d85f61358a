# 项目独立 AES 密钥功能

## 功能概述

现在每个项目都可以设置独立的 AES 加密密钥，实现数据加密的项目级隔离。这提供了更高的安全性和灵活性。

## 主要特性

### 1. 独立密钥管理

- 每个项目都有自己的 AES 密钥字段
- 创建项目时可以指定自定义密钥
- 可以随时更新项目的 AES 密钥
- 如果不指定密钥，使用默认密钥 `zmxy2024cloudkey`

### 2. 自动密钥处理

- 系统自动处理密钥长度（AES-128 需要 16 字节）
- 密钥过长时自动截取前 16 字节
- 密钥过短时自动用 0 填充到 16 字节

### 3. 项目特定加密 API

- 新增 API 端点：`GET /api/projects/{projectId}/encrypted`
- 使用项目自己的 AES 密钥进行数据加密
- 保持与原有 API 的兼容性

## API 接口

### 创建项目（支持自定义 AES 密钥）

```http
POST /api/projects
Content-Type: application/json

{
  "id": "my_project",
  "name": "我的项目",
  "description": "项目描述",
  "aes_key": "my_custom_key_16!"
}
```

### 更新项目（包括 AES 密钥）

```http
PUT /api/projects/{projectId}
Content-Type: application/json

{
  "name": "更新的项目名",
  "description": "更新的描述",
  "aes_key": "new_key_16bytes!"
}
```

### 获取项目特定的加密数据

```http
GET /api/projects/{projectId}/encrypted
```

## 前端界面

### 项目管理界面更新

- 项目列表中显示 AES 密钥列
- 创建/编辑项目时可以设置 AES 密钥
- 密钥输入框使用等宽字体显示
- 提供密钥用途说明

### 表格列

- **AES 密钥**：显示项目的加密密钥（180px 宽度）
- 支持响应式设计，在小屏幕上可以滚动查看

## 数据库变更

### projects 表新增字段

```sql
ALTER TABLE projects
ADD COLUMN aes_key VARCHAR(32) DEFAULT 'zmxy2024cloudkey'
AFTER description;
```

### 自动迁移

- 系统启动时自动检查并添加 aes_key 字段
- 现有项目自动使用默认密钥
- 向后兼容，不影响现有数据

## 使用示例

### 1. 创建使用自定义密钥的项目

```bash
curl -X POST http://localhost:18082/api/projects \
  -H "Content-Type: application/json" \
  -d '{
    "id": "secure_project",
    "name": "安全项目",
    "description": "使用自定义密钥的项目",
    "aes_key": "secure_key_16b!"
  }'
```

### 2. 获取项目特定的加密数据

```bash
curl http://localhost:18082/api/projects/secure_project/encrypted
```

### 3. 更新项目密钥

```bash
curl -X PUT http://localhost:18082/api/projects/secure_project \
  -H "Content-Type: application/json" \
  -d '{
    "name": "安全项目",
    "description": "使用自定义密钥的项目",
    "aes_key": "new_secure_key!"
  }'
```

## 安全考虑

### 密钥管理

- 密钥存储在数据库中，建议在生产环境中加密存储
- 不同项目使用不同密钥，提高安全隔离性
- 支持密钥轮换，可以随时更新项目密钥

### 兼容性

- 保持与原有 `/zmxycons` API 的完全兼容
- 原有项目继续使用默认密钥
- 新项目可以选择使用自定义密钥

## 测试验证

系统已通过以下测试：

1. ✅ 创建带自定义密钥的项目
2. ✅ 不同项目生成不同的加密数据
3. ✅ 更新项目密钥功能
4. ✅ 密钥更新后加密数据改变
5. ✅ 前端界面正确显示和编辑密钥
6. ✅ 数据库自动迁移功能
7. ✅ API 兼容性验证

## 问题修复

### 1. 前端 AES 密钥管理

✅ **已修复**：前端项目管理界面完整支持 AES 密钥功能

- 项目列表正确显示 AES 密钥列（等宽字体）
- 创建项目时可以设置自定义 AES 密钥
- 编辑项目时可以修改 AES 密钥
- 添加了 AES 密钥长度验证（最大 32 字符）

### 2. 对象类型校验错误信息

✅ **已修复**：改进了数据项类型校验的错误提示

- 数组类型：显示 "数组支持 [1,2,"3"] 或 {1,2,"3"} 格式"
- 对象类型：显示 "对象支持 {"key":"value"} 格式"
- 其他类型：显示 "请输入有效的 JSON 格式"

## 功能验证

### ✅ 前端 AES 密钥管理测试

1. **项目列表显示**：正确显示所有项目的 AES 密钥
2. **创建项目**：可以在创建时设置自定义密钥
3. **编辑项目**：可以修改现有项目的密钥
4. **表单验证**：密钥长度限制在 32 字符以内

### ✅ 对象类型数据项测试

1. **创建对象数据**：成功创建对象类型的数据项
2. **数据存储**：对象数据正确序列化为 JSON 存储
3. **错误提示**：对象格式错误时显示正确的提示信息

### ✅ 项目独立加密测试

1. **不同密钥生成不同加密数据**：验证通过
2. **密钥更新后加密数据改变**：验证通过
3. **API 兼容性**：保持与原有系统完全兼容

## 总结

项目独立 AES 密钥功能已成功实现并修复了所有问题，提供了：

- 🔐 **更高安全性**：每个项目独立的加密密钥
- 🔧 **灵活配置**：支持自定义密钥和密钥更新
- 🔄 **完全兼容**：保持与现有系统的兼容性
- 🎯 **易于使用**：友好的前端界面和 API 接口
- ✨ **完善体验**：准确的错误提示和表单验证
