package main

import (
	appconfig "cloud-data-manager/config"
	"cloud-data-manager/controllers"
	"cloud-data-manager/database"
	"cloud-data-manager/middleware"
	"log"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

func main() {
	// 加载配置
	log.Println("Loading configuration...")
	err := appconfig.LoadConfig("config/config.json")
	if err != nil {
		log.Fatal("Failed to load config:", err)
	}
	log.Println("Configuration loaded successfully")

	// 初始化数据库
	log.Println("Initializing database...")
	err = database.InitDB(appconfig.GlobalConfig.GetDSN())
	if err != nil {
		log.Fatal("Failed to initialize database:", err)
	}
	defer database.CloseDB()
	log.Println("Database initialized successfully")

	// 创建数据库表
	log.Println("Creating database tables...")
	err = database.CreateTables()
	if err != nil {
		log.Fatal("Failed to create tables:", err)
	}
	log.Println("Database tables created successfully")

	// 初始化默认数据
	err = database.InitDefaultData()
	if err != nil {
		log.Printf("Warning: Failed to initialize default data: %v", err)
	}

	// 设置Gin模式
	if !appconfig.GlobalConfig.System.Debug {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建路由
	r := gin.Default()

	// CORS配置
	config := cors.DefaultConfig()
	config.AllowOrigins = appconfig.GlobalConfig.System.AllowedOrigins
	config.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}
	config.AllowHeaders = []string{"Origin", "Content-Type", "Accept", "Authorization", "X-Requested-With"}
	config.AllowCredentials = true
	r.Use(cors.New(config))

	// 创建控制器实例
	projectController := controllers.NewProjectController()
	dataController := controllers.NewDataController()
	luaController := controllers.NewLuaController()
	authController := controllers.NewAuthController()

	// 原始数据API和加密API已删除

	// 认证路由（无需鉴权）
	authGroup := r.Group("/api/auth")
	{
		authGroup.POST("/login", authController.Login)
		authGroup.POST("/logout", authController.Logout)
		authGroup.GET("/user", authController.GetUserInfo)
		authGroup.POST("/refresh", authController.RefreshToken)
		authGroup.GET("/check", authController.CheckAuth)
	}

	// 添加认证中间件（仅对需要鉴权的API生效）
	authRequired := r.Group("/")
	authRequired.Use(middleware.AuthMiddleware())

	// API路由组（需要鉴权）
	api := authRequired.Group("/api")
	{
		// 项目管理
		projects := api.Group("/projects")
		{
			projects.GET("", projectController.GetAllProjects)
			projects.POST("", projectController.CreateProject)
			projects.GET("/:projectId", projectController.GetProject)
			projects.PUT("/:projectId", projectController.UpdateProject)
			projects.DELETE("/:projectId", projectController.DeleteProject)

			// 数据表管理
			projects.GET("/:projectId/tables", dataController.GetDataTables)
			projects.POST("/:projectId/tables", dataController.CreateDataTable)
			projects.PUT("/:projectId/tables/:tableName", dataController.UpdateDataTable)
			projects.DELETE("/:projectId/tables/:tableName", dataController.DeleteDataTable)

			// 数据项管理
			projects.GET("/:projectId/tables/:tableName/items", dataController.GetDataItems)
			projects.POST("/:projectId/tables/:tableName/items", dataController.CreateDataItem)
			projects.PUT("/:projectId/tables/:tableName/items/:id", dataController.UpdateDataItem)
			projects.DELETE("/:projectId/tables/:tableName/items/:id", dataController.DeleteDataItem)

			// Lua数据导出
			projects.GET("/:projectId/lua", luaController.GetLuaData)
			// Lua数据预览（无需权限验证）
			projects.GET("/:projectId/preview", luaController.GetLuaDataPreview)
		}
	}

	// 通用加密API和动态调试API已删除，提高安全性

	// 静态文件服务（用于前端）
	r.Static("/assets", appconfig.GlobalConfig.Server.StaticPath+"/assets")
	r.StaticFile("/", appconfig.GlobalConfig.Server.StaticPath+"/index.html")
	r.StaticFile("/favicon.ico", appconfig.GlobalConfig.Server.StaticPath+"/favicon.ico")

	// 启动服务器
	serverAddr := appconfig.GlobalConfig.GetServerAddr()

	log.Printf("Server starting...")
	log.Printf("Listening on: %s", serverAddr)
	log.Printf("API endpoints:")
	log.Printf("  - Management UI: http://localhost:%d", appconfig.GlobalConfig.Server.Port)
	log.Printf("  - API base: http://localhost:%d/api", appconfig.GlobalConfig.Server.Port)
	log.Printf("  - Legacy API: http://localhost:%d%s", appconfig.GlobalConfig.Server.Port, appconfig.GlobalConfig.Server.LegacyAPIPath)

	if appconfig.GlobalConfig.IsAuthEnabled() {
		log.Printf("Authentication: ENABLED")
		log.Printf("  - Username: %s", appconfig.GlobalConfig.Auth.Username)
		log.Printf("  - Login URL: http://localhost:%d/api/auth/login", appconfig.GlobalConfig.Server.Port)
	} else {
		log.Printf("Authentication: DISABLED")
	}

	if err := r.Run(serverAddr); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}
