package services

import (
	"encoding/json"
	"fmt"
	"strings"
)

// LuaService Lua数据服务
type LuaService struct {
	dataItemService *DataItemService
	projectService  *ProjectService
}

// NewLuaService 创建Lua数据服务实例
func NewLuaService() *LuaService {
	return &LuaService{
		dataItemService: NewDataItemService(),
		projectService:  NewProjectService(),
	}
}

// GenerateLuaData 生成指定项目的Lua格式数据
func (s *LuaService) GenerateLuaData(projectID string) (string, error) {
	// 获取项目的所有数据表
	tableService := NewDataTableService()
	tables, err := tableService.GetDataTablesByProject(projectID)
	if err != nil {
		return "", fmt.Errorf("failed to get data tables: %v", err)
	}

	var luaBuilder strings.Builder
	luaBuilder.WriteString(fmt.Sprintf("%s = {\n", projectID))

	for i, table := range tables {
		// 获取数据表的所有数据项
		items, err := s.dataItemService.GetDataItems(projectID, table.TableName)
		if err != nil {
			return "", fmt.Errorf("failed to get data items for table %s: %v", table.TableName, err)
		}

		luaBuilder.WriteString(fmt.Sprintf("\t%s = {", table.TableName))

		if len(items) == 0 {
			luaBuilder.WriteString("}")
		} else {
			luaBuilder.WriteString("\n")

			for j, item := range items {
				// 解析JSON值
				var value interface{}
				itemValueBytes := []byte(item.ItemValue)
				err := json.Unmarshal(itemValueBytes, &value)
				if err != nil {
					// 如果JSON解析失败，尝试修复常见问题
					fixedValue := fixJSONValue(item.ItemValue)
					err = json.Unmarshal([]byte(fixedValue), &value)
					if err != nil {
						return "", fmt.Errorf("failed to unmarshal item value for key '%s' in table '%s': value='%s', error: %v", item.ItemKey, table.TableName, item.ItemValue, err)
					}
				}

				// 转换为Lua格式
				luaValue, err := s.convertToLuaValue(value)
				if err != nil {
					return "", fmt.Errorf("failed to convert value to Lua: %v", err)
				}

				luaBuilder.WriteString(fmt.Sprintf("\t\t%s = %s", item.ItemKey, luaValue))

				if j < len(items)-1 {
					luaBuilder.WriteString(" ,")
				}
				luaBuilder.WriteString("\n")
			}
			luaBuilder.WriteString("\t}")
		}

		if i < len(tables)-1 {
			luaBuilder.WriteString(" ,")
		}
		luaBuilder.WriteString("\n")
	}

	luaBuilder.WriteString("}")
	return luaBuilder.String(), nil
}



// convertToLuaValue 将Go值转换为Lua格式字符串
func (s *LuaService) convertToLuaValue(value interface{}) (string, error) {
	switch v := value.(type) {
	case []interface{}:
		var parts []string
		for _, item := range v {
			luaItem, err := s.convertToLuaValue(item)
			if err != nil {
				return "", err
			}
			parts = append(parts, luaItem)
		}
		return "{" + strings.Join(parts, " , ") + "}", nil
	case map[string]interface{}:
		var parts []string
		for key, val := range v {
			luaVal, err := s.convertToLuaValue(val)
			if err != nil {
				return "", err
			}
			parts = append(parts, fmt.Sprintf("%s = %s", key, luaVal))
		}
		return "{" + strings.Join(parts, " , ") + "}", nil
	case string:
		return fmt.Sprintf(`"%s"`, v), nil
	case float64:
		if v == float64(int64(v)) {
			return fmt.Sprintf("%.0f", v), nil
		}
		return fmt.Sprintf("%g", v), nil
	case bool:
		if v {
			return "true", nil
		}
		return "false", nil
	case nil:
		return "nil", nil
	default:
		return fmt.Sprintf("%v", v), nil
	}
}



// fixJSONValue 修复常见的JSON格式问题
func fixJSONValue(value string) string {
	// 去除前后空格
	value = strings.TrimSpace(value)

	// 如果是空字符串，返回空字符串的JSON
	if value == "" {
		return `""`
	}

	// 如果已经是有效的JSON，直接返回
	var testValue interface{}
	if json.Unmarshal([]byte(value), &testValue) == nil {
		return value
	}

	// 修复常见的布尔值问题
	switch value {
	case "true", "True", "TRUE":
		return "true"
	case "false", "False", "FALSE":
		return "false"
	case "tru", "Tru":
		return "true"
	case "fals", "Fals":
		return "false"
	}

	// 检查是否是数字
	var num float64
	if json.Unmarshal([]byte(value), &num) == nil {
		return value
	}

	// 如果不是数字且没有引号，当作字符串处理
	if len(value) > 0 && value[0] != '"' && value[0] != '{' && value[0] != '[' {
		// 转义内部的引号
		escaped := strings.ReplaceAll(value, `"`, `\"`)
		return fmt.Sprintf(`"%s"`, escaped)
	}

	return value
}
