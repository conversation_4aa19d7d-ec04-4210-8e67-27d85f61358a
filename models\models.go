package models

import (
	"time"
)

// Project 项目模型
type Project struct {
	ID               string    `json:"id" db:"id"`
	Name             string    `json:"name" db:"name"`
	Description      string    `json:"description" db:"description"`
	AESKey           string    `json:"aes_key" db:"aes_key"`
	CreatedAt        time.Time `json:"created_at" db:"created_at"`
	UpdatedAt        time.Time `json:"updated_at" db:"updated_at"`
}

// DataTable 数据表模型
type DataTable struct {
	ID          int       `json:"id" db:"id"`
	ProjectID   string    `json:"project_id" db:"project_id"`
	TableName   string    `json:"table_name" db:"table_name"`
	Description string    `json:"description" db:"description"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

// DataItem 数据项模型
type DataItem struct {
	ID        int       `json:"id" db:"id"`
	ProjectID string    `json:"project_id" db:"project_id"`
	TableName string    `json:"table_name" db:"table_name"`
	ItemKey   string    `json:"item_key" db:"item_key"`
	ItemValue string    `json:"item_value" db:"item_value"`
	ValueType string    `json:"value_type" db:"value_type"`
	CreatedAt time.Time `json:"created_at" db:"created_at"`
	UpdatedAt time.Time `json:"updated_at" db:"updated_at"`
}

// CreateProjectRequest 创建项目请求
type CreateProjectRequest struct {
	ID              string `json:"id" binding:"required"`
	Name            string `json:"name" binding:"required"`
	Description     string `json:"description"`
	AESKey          string `json:"aes_key"`
}

// UpdateProjectRequest 更新项目请求
type UpdateProjectRequest struct {
	Name            string `json:"name" binding:"required"`
	Description     string `json:"description"`
	AESKey          string `json:"aes_key"`
}

// CreateDataTableRequest 创建数据表请求
type CreateDataTableRequest struct {
	TableName   string `json:"table_name" binding:"required"`
	Description string `json:"description"`
}

// CreateDataItemRequest 创建数据项请求
type CreateDataItemRequest struct {
	ItemKey   string      `json:"item_key" binding:"required"`
	ItemValue interface{} `json:"item_value" binding:"required"`
	ValueType string      `json:"value_type"`
}

// UpdateDataItemRequest 更新数据项请求
type UpdateDataItemRequest struct {
	ItemValue interface{} `json:"item_value" binding:"required"`
	ValueType string      `json:"value_type"`
}

// LuaExportData Lua导出数据结构
type LuaExportData struct {
	ProjectID string                            `json:"project_id"`
	Tables    map[string]map[string]interface{} `json:"tables"`
}
