<template>
  <div class="data-management">
    <div class="page-header">
      <h2 class="page-title">数据管理</h2>
      <a-button type="primary" @click="openTableModal()" class="create-btn">
        <template #icon>
          <plus-outlined />
        </template>
        <span class="btn-text">新建数据表</span>
      </a-button>
    </div>

    <!-- 数据表标签页 -->
    <div v-if="tables.length > 0" class="tables-container">
      <a-tabs
        v-model:activeKey="activeTable"
        type="editable-card"
        @edit="onTabEdit"
        @change="handleTabChange"
        class="data-tabs"
      >
        <a-tab-pane
          v-for="table in tables"
          :key="table.name"
          :tab="table.name"
          :closable="true"
        >
          <DataItemList
            :project-id="projectId"
            :table-name="table.name"
            :table-info="table"
            @refresh="loadTables"
          />
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 无数据表时的提示 -->
    <div v-else class="empty-state">
      <a-empty description="暂无数据表">
        <template #image>
          <a-icon type="table" class="empty-icon" />
        </template>
        <a-button type="primary" @click="openTableModal()">
          创建第一个数据表
        </a-button>
      </a-empty>
    </div>

    <!-- 新建/编辑数据表模态框 -->
    <a-modal
      v-model:open="tableModalVisible"
      :title="editingTable ? '编辑数据表' : '新建数据表'"
      @ok="handleTableSubmit"
      @cancel="handleTableCancel"
    >
      <a-form
        ref="tableFormRef"
        :model="tableForm"
        :rules="tableRules"
        layout="vertical"
      >
        <a-form-item label="表名" name="name">
          <a-input
            v-model:value="tableForm.name"
            placeholder="请输入表名"
            :disabled="!!editingTable"
          />
        </a-form-item>
        <a-form-item label="描述" name="description">
          <a-textarea
            v-model:value="tableForm.description"
            placeholder="请输入表描述"
            :rows="4"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { dataTableAPI } from '../services/api'
import DataItemList from './DataItemList.vue'

const props = defineProps({
  projectId: {
    type: String,
    required: true
  }
})

// 响应式数据
const tables = ref([])
const activeTable = ref('')
const loading = ref(false)
const tableModalVisible = ref(false)
const editingTable = ref(null)
const tableFormRef = ref()

// 表单数据
const tableForm = ref({
  name: '',
  description: ''
})

// 表单验证规则
const tableRules = {
  name: [
    { required: true, message: '请输入表名' },
    { pattern: /^[a-zA-Z0-9_\u4e00-\u9fa5]+$/, message: '表名只能包含字母、数字、下划线和中文' }
  ]
}

// 加载数据表列表
const loadTables = async () => {
  if (!props.projectId) return

  try {
    loading.value = true
    const response = await dataTableAPI.getByProject(props.projectId)
    // 转换后端字段名以匹配前端期望
    tables.value = (response.data || []).map(table => ({
      ...table,
      name: table.table_name // 将 table_name 映射为 name
    }))

    // 如果没有选中的表且有表列表，选择第一个
    if (!activeTable.value && tables.value.length > 0) {
      activeTable.value = tables.value[0].name
    }
  } catch (error) {
    message.error('加载数据表列表失败')
    console.error('Failed to load tables:', error)
  } finally {
    loading.value = false
  }
}

// 打开数据表模态框
const openTableModal = (table = null) => {
  console.log('openTableModal called with:', table)
  editingTable.value = table
  if (table) {
    tableForm.value = { ...table }
  } else {
    tableForm.value = {
      name: '',
      description: ''
    }
  }
  console.log('Setting tableModalVisible to true')
  tableModalVisible.value = true
  console.log('tableModalVisible is now:', tableModalVisible.value)
}

// 处理数据表表单提交
const handleTableSubmit = async () => {
  try {
    await tableFormRef.value.validate()

    // 转换字段名以匹配后端API
    const requestData = {
      table_name: tableForm.value.name,
      description: tableForm.value.description
    }

    if (editingTable.value) {
      await dataTableAPI.update(props.projectId, editingTable.value.name, requestData)
      message.success('数据表更新成功')
    } else {
      await dataTableAPI.create(props.projectId, requestData)
      message.success('数据表创建成功')
    }

    tableModalVisible.value = false
    loadTables()
  } catch (error) {
    if (error.errorFields) {
      // 表单验证错误
      return
    }
    console.error('Failed to save table:', error)
    if (error.response && error.response.data && error.response.data.error) {
      message.error(`操作失败: ${error.response.data.error}`)
    } else {
      message.error(editingTable.value ? '数据表更新失败' : '数据表创建失败')
    }
  }
}

// 取消数据表操作
const handleTableCancel = () => {
  tableModalVisible.value = false
  editingTable.value = null
}

// 处理标签页编辑
const onTabEdit = async (targetKey, action) => {
  console.log('onTabEdit called with:', { targetKey, action })

  if (action === 'add') {
    // 点击加号按钮，打开新建数据表模态框
    openTableModal()
  } else if (action === 'remove') {
    try {
      await dataTableAPI.delete(props.projectId, targetKey)
      message.success('数据表删除成功')
      loadTables()

      // 如果删除的是当前活动标签，切换到第一个标签
      if (activeTable.value === targetKey) {
        const remainingTables = tables.value.filter(t => t.name !== targetKey)
        activeTable.value = remainingTables.length > 0 ? remainingTables[0].name : ''
      }
    } catch (error) {
      message.error('数据表删除失败')
      console.error('Failed to delete table:', error)
    }
  }
}

// 处理标签页切换
const handleTabChange = (key) => {
  activeTable.value = key
}

// 监听项目ID变化
watch(() => props.projectId, () => {
  if (props.projectId) {
    loadTables()
  }
}, { immediate: true })

// 组件挂载时加载数据
onMounted(() => {
  if (props.projectId) {
    loadTables()
  }
})
</script>

<style scoped>
.data-management {
  width: 100%;
  max-width: 100%;
}

.page-header {
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #262626;
}

.create-btn {
  flex-shrink: 0;
}

.tables-container {
  width: 100%;
  overflow-x: auto;
}

.data-tabs {
  width: 100%;
}

.data-tabs :deep(.ant-tabs-nav) {
  margin-bottom: 16px;
}

.data-tabs :deep(.ant-tabs-tab) {
  padding: 8px 16px;
  min-width: 120px;
  text-align: center;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.empty-icon {
  font-size: 64px;
  color: #d9d9d9;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .page-title {
    font-size: 20px;
    text-align: center;
  }

  .create-btn {
    width: 100%;
  }

  .btn-text {
    margin-left: 4px;
  }

  .data-tabs :deep(.ant-tabs-tab) {
    min-width: 100px;
    padding: 6px 12px;
  }

  .empty-state {
    padding: 40px 16px;
  }
}

@media (max-width: 576px) {
  .page-title {
    font-size: 18px;
  }

  .data-tabs :deep(.ant-tabs-tab) {
    min-width: 80px;
    padding: 4px 8px;
    font-size: 12px;
  }

  .empty-state {
    padding: 30px 12px;
  }

  .empty-icon {
    font-size: 48px;
  }
}

/* 防止标签页溢出 */
.data-tabs :deep(.ant-tabs-nav-wrap) {
  overflow-x: auto;
  overflow-y: hidden;
}

.data-tabs :deep(.ant-tabs-nav-list) {
  white-space: nowrap;
}
</style>
