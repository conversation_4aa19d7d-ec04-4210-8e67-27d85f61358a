package controllers

import (
	"cloud-data-manager/config"
	"cloud-data-manager/middleware"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// AuthController 认证控制器
type AuthController struct{}

// NewAuthController 创建认证控制器实例
func NewAuthController() *AuthController {
	return &AuthController{}
}

// LoginRequest 登录请求结构
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// LoginResponse 登录响应结构
type LoginResponse struct {
	Token    string `json:"token"`
	Username string `json:"username"`
	ExpiresAt int64  `json:"expires_at"`
}

// Login 用户登录
func (ac *AuthController) Login(ctx *gin.Context) {
	var req LoginRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": "请求参数错误",
			"details": err.Error(),
		})
		return
	}

	// 验证用户名和密码
	if req.Username != config.GlobalConfig.Auth.Username || 
	   req.Password != config.GlobalConfig.Auth.Password {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"error": "用户名或密码错误",
			"code": "INVALID_CREDENTIALS",
		})
		return
	}

	// 生成JWT令牌
	token, err := middleware.GenerateToken(req.Username)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error": "生成令牌失败",
			"details": err.Error(),
		})
		return
	}

	// 设置Cookie
	ctx.SetCookie(
		config.GlobalConfig.Auth.SessionName,
		token,
		int(time.Duration(config.GlobalConfig.Auth.TokenExpiry) * time.Hour / time.Second),
		"/",
		"",
		false,
		true,
	)

	// 返回响应
	ctx.JSON(http.StatusOK, gin.H{
		"message": "登录成功",
		"data": LoginResponse{
			Token:     token,
			Username:  req.Username,
			ExpiresAt: time.Now().Add(time.Duration(config.GlobalConfig.Auth.TokenExpiry) * time.Hour).Unix(),
		},
	})
}

// Logout 用户登出
func (ac *AuthController) Logout(ctx *gin.Context) {
	// 清除Cookie
	ctx.SetCookie(
		config.GlobalConfig.Auth.SessionName,
		"",
		-1,
		"/",
		"",
		false,
		true,
	)

	ctx.JSON(http.StatusOK, gin.H{
		"message": "登出成功",
	})
}

// GetUserInfo 获取用户信息
func (ac *AuthController) GetUserInfo(ctx *gin.Context) {
	username, exists := ctx.Get("username")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"error": "未找到用户信息",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"data": gin.H{
			"username": username,
			"role":     "admin",
		},
	})
}

// RefreshToken 刷新令牌
func (ac *AuthController) RefreshToken(ctx *gin.Context) {
	// 获取当前令牌
	tokenString := ctx.GetHeader("Authorization")
	if tokenString == "" {
		cookie, err := ctx.Cookie(config.GlobalConfig.Auth.SessionName)
		if err != nil {
			ctx.JSON(http.StatusUnauthorized, gin.H{
				"error": "未提供认证令牌",
			})
			return
		}
		tokenString = cookie
	}

	// 刷新令牌
	newToken, err := middleware.RefreshToken(tokenString)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"error": "令牌刷新失败",
			"details": err.Error(),
		})
		return
	}

	// 更新Cookie
	ctx.SetCookie(
		config.GlobalConfig.Auth.SessionName,
		newToken,
		int(time.Duration(config.GlobalConfig.Auth.TokenExpiry) * time.Hour / time.Second),
		"/",
		"",
		false,
		true,
	)

	ctx.JSON(http.StatusOK, gin.H{
		"message": "令牌刷新成功",
		"data": gin.H{
			"token": newToken,
		},
	})
}

// CheckAuth 检查认证状态
func (ac *AuthController) CheckAuth(ctx *gin.Context) {
	// 如果未启用认证，返回未启用状态
	if !config.GlobalConfig.IsAuthEnabled() {
		ctx.JSON(http.StatusOK, gin.H{
			"authenticated": false,
			"auth_enabled": false,
			"message": "认证未启用",
		})
		return
	}

	// 检查是否已认证
	username, exists := ctx.Get("username")
	if !exists {
		ctx.JSON(http.StatusOK, gin.H{
			"authenticated": false,
			"auth_enabled": true,
			"message": "未认证",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"authenticated": true,
		"auth_enabled": true,
		"username": username,
		"message": "已认证",
	})
}
