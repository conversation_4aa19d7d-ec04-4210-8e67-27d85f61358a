-- 删除现有的测试数据
DELETE FROM data_items WHERE project_id='test';

-- 插入正确格式的测试数据
INSERT INTO data_items (project_id, table_name, item_key, item_value) VALUES
('test', 'testtable', 'testkey', '"testvalue"'),
('test', 'testtable', 'number', '123'),
('test', 'testtable', 'boolean', 'true'),
('test', 'testtable', 'array', '[1, 2, 3]');

-- 查看插入的数据
SELECT id, item_key, item_value FROM data_items WHERE project_id='test';
