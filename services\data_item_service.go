package services

import (
	"cloud-data-manager/database"
	"cloud-data-manager/models"
	"database/sql"
	"encoding/json"
	"fmt"
	"reflect"
)

// DataItemService 数据项服务
type DataItemService struct{}

// NewDataItemService 创建数据项服务实例
func NewDataItemService() *DataItemService {
	return &DataItemService{}
}

// GetDataItems 获取数据表的所有数据项
func (s *DataItemService) GetDataItems(projectID, tableName string) ([]models.DataItem, error) {
	query := `SELECT id, project_id, table_name, item_key, item_value, value_type, created_at, updated_at 
			  FROM data_items WHERE project_id = ? AND table_name = ? ORDER BY item_key ASC`
	rows, err := database.DB.Query(query, projectID, tableName)
	if err != nil {
		return nil, fmt.Errorf("failed to query data items: %v", err)
	}
	defer rows.Close()

	var items []models.DataItem
	for rows.Next() {
		var item models.DataItem
		err := rows.Scan(&item.ID, &item.ProjectID, &item.TableName, &item.ItemKey, 
						&item.ItemValue, &item.ValueType, &item.CreatedAt, &item.UpdatedAt)
		if err != nil {
			return nil, fmt.Errorf("failed to scan data item: %v", err)
		}
		items = append(items, item)
	}

	return items, nil
}

// GetDataItem 获取指定数据项
func (s *DataItemService) GetDataItem(projectID, tableName, itemKey string) (*models.DataItem, error) {
	query := `SELECT id, project_id, table_name, item_key, item_value, value_type, created_at, updated_at 
			  FROM data_items WHERE project_id = ? AND table_name = ? AND item_key = ?`
	row := database.DB.QueryRow(query, projectID, tableName, itemKey)

	var item models.DataItem
	err := row.Scan(&item.ID, &item.ProjectID, &item.TableName, &item.ItemKey, 
					&item.ItemValue, &item.ValueType, &item.CreatedAt, &item.UpdatedAt)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("data item not found")
		}
		return nil, fmt.Errorf("failed to get data item: %v", err)
	}

	return &item, nil
}

// CreateDataItem 创建数据项
func (s *DataItemService) CreateDataItem(projectID, tableName string, req models.CreateDataItemRequest) error {
	valueType := req.ValueType
	if valueType == "" {
		valueType = s.detectValueType(req.ItemValue)
	}

	valueJSON, err := json.Marshal(req.ItemValue)
	if err != nil {
		return fmt.Errorf("failed to marshal item value: %v", err)
	}

	query := `INSERT INTO data_items (project_id, table_name, item_key, item_value, value_type) 
			  VALUES (?, ?, ?, ?, ?)`
	_, err = database.DB.Exec(query, projectID, tableName, req.ItemKey, string(valueJSON), valueType)
	if err != nil {
		return fmt.Errorf("failed to create data item: %v", err)
	}

	return nil
}

// UpdateDataItem 更新数据项
func (s *DataItemService) UpdateDataItem(projectID, tableName, itemKey string, req models.UpdateDataItemRequest) error {
	valueType := req.ValueType
	if valueType == "" {
		valueType = s.detectValueType(req.ItemValue)
	}

	valueJSON, err := json.Marshal(req.ItemValue)
	if err != nil {
		return fmt.Errorf("failed to marshal item value: %v", err)
	}

	query := `UPDATE data_items SET item_value = ?, value_type = ? 
			  WHERE project_id = ? AND table_name = ? AND item_key = ?`
	result, err := database.DB.Exec(query, string(valueJSON), valueType, projectID, tableName, itemKey)
	if err != nil {
		return fmt.Errorf("failed to update data item: %v", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %v", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("data item not found")
	}

	return nil
}

// DeleteDataItem 删除数据项
func (s *DataItemService) DeleteDataItem(projectID, tableName, itemKey string) error {
	query := `DELETE FROM data_items WHERE project_id = ? AND table_name = ? AND item_key = ?`
	result, err := database.DB.Exec(query, projectID, tableName, itemKey)
	if err != nil {
		return fmt.Errorf("failed to delete data item: %v", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %v", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("data item not found")
	}

	return nil
}

// detectValueType 检测值类型
func (s *DataItemService) detectValueType(value interface{}) string {
	switch reflect.TypeOf(value).Kind() {
	case reflect.Slice, reflect.Array:
		return "array"
	case reflect.Map:
		return "object"
	case reflect.String:
		return "string"
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64,
		 reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64,
		 reflect.Float32, reflect.Float64:
		return "number"
	case reflect.Bool:
		return "boolean"
	default:
		return "string"
	}
}
