#!/bin/bash

# 云数据管理系统安装脚本

set -e

echo "=== 云数据管理系统安装脚本 ==="
echo

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "错误: 未找到Go环境，请先安装Go 1.19或更高版本"
    exit 1
fi

echo "✓ Go环境检查通过: $(go version)"

# 检查Node.js环境
if ! command -v node &> /dev/null; then
    echo "错误: 未找到Node.js环境，请先安装Node.js 16或更高版本"
    exit 1
fi

echo "✓ Node.js环境检查通过: $(node --version)"

# 检查npm
if ! command -v npm &> /dev/null; then
    echo "错误: 未找到npm，请确保Node.js安装正确"
    exit 1
fi

echo "✓ npm环境检查通过: $(npm --version)"

# 检查MySQL连接
echo "检查MySQL数据库连接..."
read -p "请输入MySQL连接字符串 (默认: root:password@tcp(localhost:3306)/cloud_data): " DB_URL
if [ -z "$DB_URL" ]; then
    DB_URL="root:password@tcp(localhost:3306)/cloud_data?charset=utf8mb4&parseTime=True&loc=Local"
fi

export DATABASE_URL="$DB_URL"

# 创建数据库（如果不存在）
echo "创建数据库..."
mysql -u root -p -e "CREATE DATABASE IF NOT EXISTS cloud_data CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" || {
    echo "警告: 无法自动创建数据库，请手动创建数据库 'cloud_data'"
}

# 安装Go依赖
echo "安装Go依赖..."
go mod tidy

# 构建后端服务
echo "构建后端服务..."
go build -o bin/server server.go

# 构建迁移工具
echo "构建数据迁移工具..."
go build -o bin/migrate_data scripts/migrate_data.go

# 安装前端依赖
echo "安装前端依赖..."
cd web
npm install

# 构建前端
echo "构建前端..."
npm run build
cd ..

# 创建启动脚本
echo "创建启动脚本..."
cat > start.sh << 'EOF'
#!/bin/bash

# 设置数据库连接
export DATABASE_URL="${DATABASE_URL:-root:password@tcp(localhost:3306)/cloud_data?charset=utf8mb4&parseTime=True&loc=Local}"

# 启动服务器
echo "启动云数据管理系统..."
echo "数据库连接: $DATABASE_URL"
echo "服务地址: http://localhost:18082"
echo "API地址: http://localhost:18082/api"
echo "兼容API: http://localhost:18082/zmxycons"
echo

./bin/server
EOF

chmod +x start.sh

# 创建迁移脚本
cat > migrate.sh << 'EOF'
#!/bin/bash

# 设置数据库连接
export DATABASE_URL="${DATABASE_URL:-root:password@tcp(localhost:3306)/cloud_data?charset=utf8mb4&parseTime=True&loc=Local}"

# 运行数据迁移
echo "运行数据迁移..."
if [ -f "参数.lua" ]; then
    echo "发现参数.lua文件，开始迁移..."
    ./bin/migrate_data 参数.lua
    echo "数据迁移完成！"
else
    echo "未找到参数.lua文件，跳过数据迁移"
    echo "如需迁移现有数据，请将参数.lua文件放在当前目录下，然后运行："
    echo "  ./bin/migrate_data 参数.lua"
fi
EOF

chmod +x migrate.sh

echo
echo "=== 安装完成 ==="
echo
echo "下一步操作："
echo "1. 确保MySQL服务正在运行"
echo "2. 运行数据迁移（如果有现有数据）："
echo "   ./migrate.sh"
echo "3. 启动服务："
echo "   ./start.sh"
echo
echo "服务地址："
echo "  - 管理界面: http://localhost:18082"
echo "  - API接口: http://localhost:18082/api"
echo "  - 兼容接口: http://localhost:18082/zmxycons"
echo
echo "如需修改数据库连接，请设置环境变量 DATABASE_URL"
echo
