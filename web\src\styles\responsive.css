/* 全局响应式样式 */

/* 基础重置 */
* {
  box-sizing: border-box;
}

html {
  font-size: 14px;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  line-height: 1.5;
  color: #333;
  background-color: #f5f5f5;
}

/* 确保全屏布局 */
html, body {
  width: 100%;
  height: 100%;
}

#app {
  width: 100%;
  height: 100%;
}

/* Ant Design 组件响应式优化 */
.ant-layout {
  width: 100%;
  height: 100%;
}

.ant-table-wrapper {
  width: 100%;
}

.ant-table {
  width: 100%;
  min-width: 600px;
}

.ant-modal {
  max-width: calc(100vw - 32px);
  margin: 16px auto;
}

.ant-modal-content {
  max-height: calc(100vh - 32px);
  overflow-y: auto;
}

.ant-form-item-label {
  white-space: nowrap;
}

/* 响应式断点 */
@media (max-width: 1920px) {
  html {
    font-size: 14px;
  }
}

@media (max-width: 1400px) {
  html {
    font-size: 13px;
  }
}

@media (max-width: 1200px) {
  html {
    font-size: 13px;
  }
  
  .ant-table {
    min-width: 500px;
  }
}

@media (max-width: 992px) {
  html {
    font-size: 12px;
  }
  
  .ant-table {
    min-width: 400px;
  }
  
  .ant-modal {
    max-width: calc(100vw - 16px);
    margin: 8px auto;
  }
}

@media (max-width: 768px) {
  html {
    font-size: 12px;
  }
  
  .ant-table {
    min-width: 350px;
  }
  
  .ant-modal {
    max-width: calc(100vw - 16px);
    margin: 8px auto;
  }
  
  .ant-form-item-label {
    text-align: left !important;
    padding-bottom: 4px;
  }
  
  .ant-form-item-control {
    margin-left: 0 !important;
  }
  
  .ant-btn {
    height: 36px;
    padding: 0 12px;
    font-size: 12px;
  }
  
  .ant-input {
    height: 36px;
    font-size: 12px;
  }
  
  .ant-select {
    font-size: 12px;
  }
  
  .ant-select-selector {
    height: 36px !important;
  }
  
  .ant-select-selection-item {
    line-height: 34px !important;
  }
}

@media (max-width: 576px) {
  html {
    font-size: 11px;
  }
  
  .ant-table {
    min-width: 300px;
  }
  
  .ant-modal {
    max-width: calc(100vw - 8px);
    margin: 4px auto;
  }
  
  .ant-btn {
    height: 32px;
    padding: 0 8px;
    font-size: 11px;
  }
  
  .ant-input {
    height: 32px;
    font-size: 11px;
  }
  
  .ant-select-selector {
    height: 32px !important;
  }
  
  .ant-select-selection-item {
    line-height: 30px !important;
  }
  
  .ant-form-item {
    margin-bottom: 16px;
  }
  
  .ant-card-head-title {
    font-size: 14px;
  }
  
  .ant-tabs-tab {
    padding: 8px 12px;
    font-size: 11px;
  }
}

/* 大屏幕优化 */
@media (min-width: 1920px) {
  html {
    font-size: 16px;
  }
  
  .ant-btn {
    height: 40px;
    padding: 0 20px;
    font-size: 14px;
  }
  
  .ant-input {
    height: 40px;
    font-size: 14px;
  }
  
  .ant-select-selector {
    height: 40px !important;
  }
  
  .ant-select-selection-item {
    line-height: 38px !important;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 移动设备滚动优化 */
@media (max-width: 768px) {
  ::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }
}

/* 确保文本不会溢出 */
.text-overflow {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-break {
  word-break: break-word;
  word-wrap: break-word;
}

/* 响应式间距 */
.responsive-margin {
  margin: 24px;
}

@media (max-width: 768px) {
  .responsive-margin {
    margin: 16px;
  }
}

@media (max-width: 576px) {
  .responsive-margin {
    margin: 12px;
  }
}

.responsive-padding {
  padding: 24px;
}

@media (max-width: 768px) {
  .responsive-padding {
    padding: 16px;
  }
}

@media (max-width: 576px) {
  .responsive-padding {
    padding: 12px;
  }
}
