<template>
  <div class="lua-export">
    <div class="page-header">
      <h2 class="page-title">Lua导出</h2>
      <a-space class="action-buttons">
        <a-button @click="loadLuaData" :loading="loading" class="refresh-btn">
          <template #icon>
            <reload-outlined />
          </template>
          <span class="btn-text">刷新预览</span>
        </a-button>

        <a-button type="primary" @click="copyToClipboard" :disabled="!displayContent" class="copy-btn">
          <template #icon>
            <copy-outlined />
          </template>
          <span class="btn-text">复制到剪贴板</span>
        </a-button>
      </a-space>
    </div>

    <a-card class="preview-card">
      <template #title>
        <span>Lua格式数据预览</span>
      </template>
      <template #extra>
        <a-space>
          <a-tag color="blue">预览模式</a-tag>
        </a-space>
      </template>

      <a-spin :spinning="loading">
        <div v-if="displayContent" class="code-container">
          <pre class="code-preview">{{ displayContent }}</pre>
        </div>
        <a-empty v-else description="点击'刷新预览'获取数据" />
      </a-spin>
    </a-card>


  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue'
import { message } from 'ant-design-vue'
import { ReloadOutlined, CopyOutlined, FileTextOutlined } from '@ant-design/icons-vue'
import { luaAPI, projectAPI } from '../services/api'

const props = defineProps({
  projectId: {
    type: String,
    required: true
  }
})

// 响应式数据
const luaContent = ref('')
const loading = ref(false)

// 计算属性
const displayContent = computed(() => {
  return luaContent.value
})



// 加载Lua数据预览
const loadLuaData = async () => {
  if (!props.projectId) return

  try {
    loading.value = true
    console.log('Loading lua data for:', props.projectId)
    const response = await luaAPI.getLuaData(props.projectId)
    console.log('Lua data response:', response)
    luaContent.value = response.data || ''
    message.success('预览数据加载成功')
  } catch (error) {
    message.error('加载Lua预览数据失败: ' + error.message)
    console.error('Failed to load lua data:', error)
    luaContent.value = ''
  } finally {
    loading.value = false
  }
}



// 复制到剪贴板
const copyToClipboard = async () => {
  if (!displayContent.value) {
    message.warning('没有可复制的内容')
    return
  }
  
  try {
    await navigator.clipboard.writeText(displayContent.value)
    message.success('已复制到剪贴板')
  } catch (error) {
    // 降级方案：使用传统方法
    const textArea = document.createElement('textarea')
    textArea.value = luaContent.value
    document.body.appendChild(textArea)
    textArea.select()
    try {
      document.execCommand('copy')
      message.success('已复制到剪贴板')
    } catch (err) {
      message.error('复制失败，请手动复制')
    }
    document.body.removeChild(textArea)
  }
}





// 监听项目ID变化
watch(() => props.projectId, () => {
  if (props.projectId) {
    loadLuaData()
  }
}, { immediate: true })

// 组件挂载时加载数据
onMounted(() => {
  if (props.projectId) {
    loadLuaData()
  }
})
</script>

<style scoped>
.lua-export {
  width: 100%;
  max-width: 100%;
}

.page-header {
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #262626;
}

.action-buttons {
  flex-shrink: 0;
}

.preview-card {
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.code-container {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.code-preview {
  background: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  max-height: 500px;
  overflow-y: auto;
  overflow-x: auto;
  white-space: pre-wrap;
  font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
  font-size: 13px;
  line-height: 1.4;
  margin: 0;
  word-break: break-word;
}

.ant-descriptions-item-content {
  word-break: break-all;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .page-title {
    font-size: 20px;
    text-align: center;
  }

  .action-buttons {
    width: 100%;
    justify-content: center;
  }

  .action-buttons .ant-btn {
    flex: 1;
    max-width: 150px;
  }

  .btn-text {
    margin-left: 4px;
  }

  .code-preview {
    font-size: 12px;
    padding: 12px;
    max-height: 300px;
  }

  .preview-card {
    margin-bottom: 16px;
  }
}

@media (max-width: 576px) {
  .page-title {
    font-size: 18px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 8px;
  }

  .action-buttons .ant-btn {
    width: 100%;
    max-width: none;
  }

  .code-preview {
    font-size: 11px;
    padding: 8px;
    max-height: 250px;
  }

  .preview-card :deep(.ant-card-head-title) {
    font-size: 14px;
  }

  .preview-card :deep(.ant-card-extra) {
    font-size: 12px;
  }
}

/* 大屏幕优化 */
@media (min-width: 1400px) {
  .code-preview {
    font-size: 14px;
    padding: 20px;
    max-height: 600px;
  }
}

/* 确保代码块在所有设备上都能正确显示 */
.code-preview {
  -webkit-overflow-scrolling: touch;
  scrollbar-width: thin;
  scrollbar-color: #ccc transparent;
}

.code-preview::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.code-preview::-webkit-scrollbar-track {
  background: transparent;
}

.code-preview::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 3px;
}

.code-preview::-webkit-scrollbar-thumb:hover {
  background: #999;
}
</style>
