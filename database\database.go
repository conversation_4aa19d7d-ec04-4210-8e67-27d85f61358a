package database

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/go-sql-driver/mysql"
)

var DB *sql.DB

// InitDB 初始化数据库连接
func InitDB(dataSourceName string) error {
	var err error
	DB, err = sql.Open("mysql", dataSourceName)
	if err != nil {
		return fmt.Errorf("failed to open database: %v", err)
	}

	if err = DB.Ping(); err != nil {
		return fmt.Errorf("failed to ping database: %v", err)
	}

	log.Println("Database connected successfully")
	return nil
}

// CloseDB 关闭数据库连接
func CloseDB() {
	if DB != nil {
		DB.Close()
	}
}

// CreateTables 创建数据库表
func CreateTables() error {
	// 创建projects表
	projectsSQL := `
	CREATE TABLE IF NOT EXISTS projects (
		id VARCHAR(50) PRIMARY KEY,
		name VARCHAR(100) NOT NULL,
		description TEXT,
		aes_key VARCHAR(32) DEFAULT 'zmxy2024cloudkey',
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
	)`

	_, err := DB.Exec(projectsSQL)
	if err != nil {
		return fmt.Errorf("failed to create projects table: %v", err)
	}

	// 创建data_tables表
	dataTablesSQL := `
	CREATE TABLE IF NOT EXISTS data_tables (
		id INT AUTO_INCREMENT PRIMARY KEY,
		project_id VARCHAR(50) NOT NULL,
		table_name VARCHAR(100) NOT NULL,
		description TEXT,
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
		FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
		UNIQUE KEY unique_project_table (project_id, table_name)
	)`

	_, err = DB.Exec(dataTablesSQL)
	if err != nil {
		return fmt.Errorf("failed to create data_tables table: %v", err)
	}

	// 创建data_items表
	dataItemsSQL := `
	CREATE TABLE IF NOT EXISTS data_items (
		id INT AUTO_INCREMENT PRIMARY KEY,
		project_id VARCHAR(50) NOT NULL,
		table_name VARCHAR(100) NOT NULL,
		item_key VARCHAR(200) NOT NULL,
		item_value TEXT,
		value_type ENUM('array', 'object', 'string', 'number', 'boolean') DEFAULT 'array',
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
		FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
		UNIQUE KEY unique_project_table_key (project_id, table_name, item_key),
		INDEX idx_project_table (project_id, table_name)
	)`

	_, err = DB.Exec(dataItemsSQL)
	if err != nil {
		return fmt.Errorf("failed to create data_items table: %v", err)
	}

	// 添加AES密钥字段（如果不存在）
	err = addAESKeyColumn()
	if err != nil {
		log.Printf("Warning: Failed to add aes_key column (may already exist): %v", err)
	}

	log.Println("Database tables created successfully")
	return nil
}

// InitDefaultData 初始化默认数据
func InitDefaultData() error {
	// 插入默认项目
	_, err := DB.Exec(`
		INSERT INTO projects (id, name, description) VALUES 
		('zmxy', '造梦西游', '造梦西游游戏数据配置项目')
		ON DUPLICATE KEY UPDATE name = VALUES(name)
	`)
	if err != nil {
		return fmt.Errorf("failed to insert default project: %v", err)
	}

	// 插入默认数据表
	_, err = DB.Exec(`
		INSERT INTO data_tables (project_id, table_name, description) VALUES 
		('zmxy', 'tuse', '图色数据表'),
		('zmxy', 'node', '节点数据表')
		ON DUPLICATE KEY UPDATE description = VALUES(description)
	`)
	if err != nil {
		return fmt.Errorf("failed to insert default data tables: %v", err)
	}

	log.Println("Default data initialized successfully")
	return nil
}

// addAESKeyColumn 添加AES密钥字段到projects表
func addAESKeyColumn() error {
	// 检查字段是否已存在
	var count int
	err := DB.QueryRow(`
		SELECT COUNT(*)
		FROM INFORMATION_SCHEMA.COLUMNS
		WHERE TABLE_SCHEMA = DATABASE()
		AND TABLE_NAME = 'projects'
		AND COLUMN_NAME = 'aes_key'
	`).Scan(&count)

	if err != nil {
		return fmt.Errorf("failed to check aes_key column existence: %v", err)
	}

	// 如果字段不存在，则添加
	if count == 0 {
		_, err = DB.Exec(`
			ALTER TABLE projects
			ADD COLUMN aes_key VARCHAR(32) DEFAULT 'zmxy2024cloudkey'
			AFTER description
		`)
		if err != nil {
			return fmt.Errorf("failed to add aes_key column: %v", err)
		}
		log.Println("Added aes_key column to projects table")
	}

	return nil
}
