package services

import (
	"cloud-data-manager/database"
	"cloud-data-manager/models"
	"database/sql"
	"fmt"
)

// DataTableService 数据表服务
type DataTableService struct{}

// NewDataTableService 创建数据表服务实例
func NewDataTableService() *DataTableService {
	return &DataTableService{}
}

// GetDataTablesByProject 获取项目的所有数据表
func (s *DataTableService) GetDataTablesByProject(projectID string) ([]models.DataTable, error) {
	query := `SELECT id, project_id, table_name, description, created_at, updated_at 
			  FROM data_tables WHERE project_id = ? ORDER BY created_at ASC`
	rows, err := database.DB.Query(query, projectID)
	if err != nil {
		return nil, fmt.Errorf("failed to query data tables: %v", err)
	}
	defer rows.Close()

	var tables []models.DataTable
	for rows.Next() {
		var table models.DataTable
		err := rows.Scan(&table.ID, &table.ProjectID, &table.TableName, &table.Description, 
						&table.CreatedAt, &table.UpdatedAt)
		if err != nil {
			return nil, fmt.Errorf("failed to scan data table: %v", err)
		}
		tables = append(tables, table)
	}

	return tables, nil
}

// GetDataTable 获取指定数据表
func (s *DataTableService) GetDataTable(projectID, tableName string) (*models.DataTable, error) {
	query := `SELECT id, project_id, table_name, description, created_at, updated_at 
			  FROM data_tables WHERE project_id = ? AND table_name = ?`
	row := database.DB.QueryRow(query, projectID, tableName)

	var table models.DataTable
	err := row.Scan(&table.ID, &table.ProjectID, &table.TableName, &table.Description, 
					&table.CreatedAt, &table.UpdatedAt)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("data table not found")
		}
		return nil, fmt.Errorf("failed to get data table: %v", err)
	}

	return &table, nil
}

// CreateDataTable 创建数据表
func (s *DataTableService) CreateDataTable(projectID string, req models.CreateDataTableRequest) error {
	query := `INSERT INTO data_tables (project_id, table_name, description) VALUES (?, ?, ?)`
	_, err := database.DB.Exec(query, projectID, req.TableName, req.Description)
	if err != nil {
		return fmt.Errorf("failed to create data table: %v", err)
	}

	return nil
}

// UpdateDataTable 更新数据表
func (s *DataTableService) UpdateDataTable(projectID, tableName string, req models.CreateDataTableRequest) error {
	query := `UPDATE data_tables SET description = ? WHERE project_id = ? AND table_name = ?`
	result, err := database.DB.Exec(query, req.Description, projectID, tableName)
	if err != nil {
		return fmt.Errorf("failed to update data table: %v", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %v", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("data table not found")
	}

	return nil
}

// DeleteDataTable 删除数据表
func (s *DataTableService) DeleteDataTable(projectID, tableName string) error {
	query := `DELETE FROM data_tables WHERE project_id = ? AND table_name = ?`
	result, err := database.DB.Exec(query, projectID, tableName)
	if err != nil {
		return fmt.Errorf("failed to delete data table: %v", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %v", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("data table not found")
	}

	return nil
}
