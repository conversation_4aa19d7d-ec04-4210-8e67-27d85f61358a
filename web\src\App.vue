<template>
  <!-- 登录页面 -->
  <Login v-if="!isAuthenticated && authEnabled" @login-success="handleLoginSuccess" />

  <!-- 主应用界面 -->
  <a-layout v-else class="main-layout">
    <!-- 顶部导航 -->
    <a-layout-header class="header">
      <div class="header-content">
        <h1 class="header-title">云数据管理系统</h1>
        <div class="header-controls">
          <span class="project-label">当前项目:</span>
          <a-select
            v-model:value="currentProject"
            class="project-select"
            placeholder="选择项目"
            @change="handleProjectChange"
            :loading="loading"
          >
            <a-select-option
              v-for="project in projects"
              :key="project.id"
              :value="project.id"
            >
              {{ project.name }}
            </a-select-option>
          </a-select>

          <!-- 用户信息和登出 -->
          <div v-if="authEnabled && currentUser" class="user-info">
            <a-dropdown>
              <a class="ant-dropdown-link" @click.prevent>
                <UserOutlined />
                {{ currentUser.username }}
                <DownOutlined />
              </a>
              <template #overlay>
                <a-menu>
                  <a-menu-item key="logout" @click="handleLogout">
                    <LogoutOutlined />
                    登出
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
        </div>
      </div>
    </a-layout-header>

    <a-layout class="content-layout">
      <!-- 侧边栏 -->
      <a-layout-sider class="sidebar" width="250">
        <a-menu
          v-model:selectedKeys="selectedKeys"
          mode="inline"
          class="sidebar-menu"
          @click="handleMenuClick"
        >
          <a-menu-item key="projects">
            <template #icon>
              <project-outlined />
            </template>
            项目管理
          </a-menu-item>
          <a-menu-item key="data" :disabled="!currentProject">
            <template #icon>
              <database-outlined />
            </template>
            数据管理
          </a-menu-item>
          <a-menu-item key="export" :disabled="!currentProject">
            <template #icon>
              <file-text-outlined />
            </template>
            Lua导出
          </a-menu-item>

        </a-menu>
      </a-layout-sider>

      <!-- 主内容区 -->
      <a-layout-content class="main-content">
        <div class="content-wrapper">
          <a-spin :spinning="loading">
            <!-- 项目管理 -->
            <ProjectManagement v-if="activeTab === 'projects'" @refresh="loadProjects" />

            <!-- 数据管理 -->
            <DataManagement
              v-if="activeTab === 'data' && currentProject"
              :project-id="currentProject"
            />

            <!-- Lua导出 -->
            <LuaExport
              v-if="activeTab === 'export' && currentProject"
              :project-id="currentProject"
            />


          </a-spin>
        </div>
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { ProjectOutlined, DatabaseOutlined, FileTextOutlined, UserOutlined, DownOutlined, LogoutOutlined } from '@ant-design/icons-vue'
import { projectAPI } from './services/api'
import { authAPI, authUtils } from './api/auth'
import ProjectManagement from './components/ProjectManagement.vue'
import DataManagement from './components/DataManagement.vue'
import LuaExport from './components/LuaExport.vue'
import Login from './components/Login.vue'

// 响应式数据
const projects = ref([])
const currentProject = ref(null)
const loading = ref(false)
const activeTab = ref('projects')
const selectedKeys = ref(['projects'])

// 认证相关状态
const isAuthenticated = ref(false)
const authEnabled = ref(true)
const currentUser = ref(null)

// 加载项目列表
const loadProjects = async () => {
  try {
    loading.value = true
    const response = await projectAPI.getAll()
    projects.value = response.data || []

    // 检查当前选中的项目是否还存在
    if (currentProject.value) {
      const projectExists = projects.value.some(p => p.id === currentProject.value)
      if (!projectExists) {
        currentProject.value = null
      }
    }

    // 如果没有选中项目且有项目列表，选择第一个
    if (!currentProject.value && projects.value.length > 0) {
      currentProject.value = projects.value[0].id
    }
  } catch (error) {
    message.error('加载项目列表失败')
    console.error('Failed to load projects:', error)
  } finally {
    loading.value = false
  }
}

// 处理项目切换
const handleProjectChange = (projectId) => {
  currentProject.value = projectId
}

// 处理菜单点击
const handleMenuClick = ({ key }) => {
  activeTab.value = key
  selectedKeys.value = [key]
}

// 检查认证状态
const checkAuthStatus = async () => {
  try {
    // 首先检查localStorage中是否有有效的token
    const user = localStorage.getItem('user')
    if (user) {
      try {
        const userData = JSON.parse(user)
        const now = Date.now() / 1000

        // 检查token是否过期
        if (userData.expiresAt && userData.expiresAt > now && userData.token) {
          // Token未过期，直接设置为已认证状态
          isAuthenticated.value = true
          currentUser.value = { username: userData.username }
          authEnabled.value = true

          // 异步验证token有效性
          try {
            const response = await authAPI.checkAuth()
            if (!response.data.authenticated) {
              // 服务器端认为token无效，清除本地存储
              localStorage.removeItem('user')
              isAuthenticated.value = false
              currentUser.value = null
            }
          } catch (error) {
            console.warn('Token验证失败，但保持本地登录状态:', error)
          }

          return
        } else {
          // Token过期，清除本地存储
          localStorage.removeItem('user')
        }
      } catch (error) {
        console.error('解析用户数据失败:', error)
        localStorage.removeItem('user')
      }
    }

    // 没有本地token或token无效，检查服务器认证状态
    const response = await authAPI.checkAuth()
    authEnabled.value = response.data.auth_enabled
    isAuthenticated.value = response.data.authenticated

    if (response.data.authenticated) {
      currentUser.value = { username: response.data.username }
    }
  } catch (error) {
    console.error('检查认证状态失败:', error)
    authEnabled.value = true
    isAuthenticated.value = false
  }
}

// 处理登录成功
const handleLoginSuccess = (userData) => {
  isAuthenticated.value = true
  currentUser.value = userData
  loadProjects()
}

// 处理登出
const handleLogout = async () => {
  try {
    await authAPI.logout()
    authUtils.clearUser()
    isAuthenticated.value = false
    currentUser.value = null
    message.success('已成功登出')
  } catch (error) {
    console.error('登出失败:', error)
    // 即使API调用失败，也清除本地状态
    authUtils.clearUser()
    isAuthenticated.value = false
    currentUser.value = null
  }
}

// 组件挂载时检查认证状态和加载数据
onMounted(async () => {
  await checkAuthStatus()

  // 如果认证已启用且已认证，或者认证未启用，则加载项目
  if (!authEnabled.value || isAuthenticated.value) {
    loadProjects()
  }
})
</script>

<style scoped>
/* 基础布局样式 */
.main-layout {
  min-height: 100vh;
  max-width: 100vw;
  overflow-x: hidden;
}

.header {
  background: #fff;
  padding: 0 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
  height: 64px;
  line-height: 64px;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  width: 100%;
}

.header-title {
  margin: 0;
  color: #1890ff;
  font-size: 20px;
  font-weight: 600;
  white-space: nowrap;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-shrink: 0;
}

.project-label {
  white-space: nowrap;
  color: #666;
}

.project-select {
  width: 200px;
  min-width: 150px;
}

.user-info {
  margin-left: 16px;
}

.user-info .ant-dropdown-link {
  color: #666;
  text-decoration: none;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.3s;
}

.user-info .ant-dropdown-link:hover {
  background-color: #f5f5f5;
  color: #1890ff;
}

.content-layout {
  width: 100%;
  height: 100%;
}

.sidebar {
  background: #fff;
  border-right: 1px solid #f0f0f0;
}

.sidebar-menu {
  height: 100%;
  border-right: 0;
}

.main-content {
  background: #f5f5f5;
  min-height: calc(100vh - 64px);
}

.content-wrapper {
  margin: 0;
  background: #fff;
  padding: 24px;
  min-height: calc(100vh - 64px);
  width: 100%;
}

/* 大屏幕优化 */
@media (min-width: 1400px) {
  .header {
    padding: 0 48px;
  }

  .content-wrapper {
    margin: 32px;
    padding: 32px;
  }

  .sidebar {
    width: 280px !important;
  }
}

@media (min-width: 1920px) {
  .header-title {
    font-size: 24px;
  }

  .project-select {
    width: 240px;
  }
}

/* 中等屏幕 */
@media (max-width: 1200px) {
  .content-wrapper {
    margin: 20px;
    padding: 20px;
  }
}

/* 小屏幕适配 */
@media (max-width: 768px) {
  .header {
    padding: 0 16px;
  }

  .header-title {
    font-size: 18px;
  }

  .sidebar {
    width: 200px !important;
  }

  .content-wrapper {
    margin: 16px;
    padding: 16px;
  }

  .project-select {
    width: 160px;
  }
}

@media (max-width: 576px) {
  .header {
    padding: 0 12px;
    height: auto;
    line-height: normal;
    padding-top: 12px;
    padding-bottom: 12px;
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .header-title {
    font-size: 16px;
  }

  .header-controls {
    width: 100%;
    justify-content: space-between;
  }

  .sidebar {
    width: 180px !important;
  }

  .content-wrapper {
    margin: 12px;
    padding: 12px;
  }

  .project-select {
    width: 140px;
  }
}

/* 防止内容溢出 */
* {
  box-sizing: border-box;
}

.ant-layout,
.ant-layout-content,
.ant-layout-sider {
  overflow-x: hidden;
}
</style>
